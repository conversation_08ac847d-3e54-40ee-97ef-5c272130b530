{"ally": {"images": {"emblem": "{name} emblem", "team_crest": "Team {name} crest"}, "navigation": {"open_navigation_menu": "Open navigation menu"}}, "admin": {"season": {"create_new_season": "Create New Season", "create_new_season_for": "Create New Season for {name}", "season_description": "This will create a new season for the competition and set it as the current season. Any leagues from the old season will be archived and transitioned to use the new season.", "start_date": "Start Date", "end_date": "End Date", "match_days": "Match Days", "external_service_id": "External Service ID", "external_service_required": "Required for KSI competitions", "creating": "Creating...", "create": "Create New Season", "cancel": "Cancel", "success": "New season created successfully", "error": "Failed to create new season. Please try again.", "validation_error": "Please select both start and end dates", "external_id_required": "Please provide the season external service ID for KSI competitions", "season_status": {"season_completed": "Season Completed", "end_date_passed": "End Date Passed", "season_active": "Season Active", "season_over_tooltip": "This season is over. You can create a new season.", "end_date_passed_tooltip": "Season end date ({date}) has passed, but {remaining} matches are not yet finished.", "season_active_tooltip": "Season is active until {date}. {completed}/{total} matches completed."}, "button_tooltip": "You can only create a new season when the current season is over"}, "feature_toggles": {"title": "Feature Toggles", "description": "Manage feature toggles for the application. Enable or disable features without deploying new code.", "application_features": "Application Features", "application_features_description": "Toggle features on or off across the application", "add_feature": "Add Feature", "feature_name": "Feature Name", "no_features": "No feature toggles found", "remove": "Remove", "enable": "Enable", "disable": "Disable"}}, "authentication": {"login": {"form": {"button": "Sign in", "button_submitting": "Signing in", "description": "Sign in to predict match scores", "errors": {"title": "Error", "unknown_error_logging_in": "Invalid email or password. Please try again.", "email_unconfirmed": "Please confirm your email before signing in", "goto_verification": "Go to Email Verification"}, "header": "Welcome to Bragging Rights", "labels": {"email": "Email address", "password": "Password"}, "no_account": {"link": "Sign up", "text": "Don't have an account?"}, "submitting": "Submitting your login details...", "successful": "Login successful! Welcome back."}}, "signup": {"form": {"button": "Sign up", "button_submitting": "Signing up", "description": "Create an account to start predicting match scores", "errors": {"email_already_in_use": "Email already in use", "invalid_email": "Invalid email", "mismatch": "Passwords do not match", "not_found": "User not found or incorrect password", "password_is_too_weak": "Password is too weak", "title": "Error", "unknown_error_creating_user": "An error occurred during sign-up. Please try again.", "username_taken": "User name is in use", "email_and_username_taken": "Email and username are in use", "password_too_short": "Password is too short (minimum is 6 characters)", "password_requires_uppercase": "Password must contain at least one uppercase letter", "password_requires_lowercase": "Password must contain at least one lowercase letter", "password_requires_number": "Password must contain at least one number", "password_requires_special_char": "Password must contain at least one special character", "validation_failed": "Please check your input and try again", "username_contains_profanity": "Username contains inappropriate language. Please choose a different username."}, "has_account": {"link": "Sign in", "text": "Already have an account?"}, "header": "Join <PERSON>", "labels": {"email": "Email address", "password": "Password", "repeat_password": "Repeat password", "username": "Username"}, "successful": "Signup successful! Welcome to Bragging Rights.", "submitting": "Submitting your signup information..."}}, "verification": {"title": "Verify Your Email", "description": "Please enter the verification code sent to your email address.", "description_with_email": "We've sent a verification code to {email}. Please enter it below.", "code_placeholder": "Enter verification code", "verify": "<PERSON><PERSON><PERSON>", "verifying": "Verifying...", "success": "Your email has been verified successfully!", "error": {"verification_failed": "Verification failed. The code may be invalid or expired.", "email_not_found": "Email address not found. Please try registering again."}, "resend": {"prompt": "Didn't receive a code?", "action": "Resend Code", "sending": "Sending...", "success": "A new verification code has been sent to your email.", "error": "Failed to resend verification code. Please try again."}}}, "competition_page": {"ally": {"previous_page": "Previous page", "next_page": "Next page", "select_page": "Select page {pageNumber}", "selected_page": "Selected page {pageNumber}", "images": {"team_crest": "Team {name} crest"}}, "area": "Area: {areaName}", "create_league": {"card": {"form": {"button": "Create league", "button_submitting": "Creating...", "input_placeholder": "Enter league name", "league_name": "League name", "open_league": "Is the league open for all?", "open_selected_open": "Open", "open_selected_closed": "Closed", "open_help_title": "Open vs Closed", "open_help_body": "Open leagues allow anyone with the link to join immediately. Closed leagues require the owner to approve applications.", "open_help_body_alt": "Open leagues allow anyone to join instantly. Closed leagues require the owner to approve join requests.", "open_registration": "Open registration", "anyone_can_join": "Anyone can join", "approval_needed": "Owner approval needed", "subscriber_only_league": "Subscriber-only league", "subscriber_only_description": "Users must be subscribed to your YouTube channel to join this league.", "subscriber_only_enabled": "Enabled", "subscriber_only_league_name_notice": "When subscriber-only is enabled, your league name will use your YouTube channel name and will be visible to participants."}, "title": "Create a New League"}, "errors": {"league_name_required": "League name cannot be empty", "title": "Error", "unknown_error_creating_league": "An error occurred during league creation. Please try again later", "league_name_contains_profanity": "League name contains inappropriate language. Please choose a different name.", "requires_subscription": "You need to be a subscriber of the channel to join this league.", "not_eligible_for_subscriber_league": "You are not eligible to create a subscriber-only league."}, "submitting": "Submitting league creation", "success": "League ''{name}'' created successfully"}, "current_holders": "Current holders:", "errors": {"joining_league": "An error occurred while joining the league", "requires_subscription": "You need to be a subscriber of the channel to join this league.", "youtube_not_connected": "You need to connect your YouTube account to join this league.", "elevate_scope_required": "You need to elevate your YouTube scope to join this league.", "max_leagues_reached": "Maximum leagues reached"}, "join_league": "Join League", "join_league_table": {"action": "Action", "ally": {"join_league_button": "Join league {leagueName}", "next_page": "Next page", "previous_page": "Previous page", "select_page": "Select page {pageNumber}", "selected_page": "Selected page {pageNumber}", "apply_league_button": "Apply to league {leagueName}"}, "league_name": "League name", "owner": "Owner", "users": "Users", "status": "Status", "open": "Open", "private": "Private", "subscribers": "Subscribers", "loading": "Loading...", "applied_to_league": "Applied to league: {name}"}, "filter_leagues": "Filter leagues", "all_leagues": "All leagues", "open_leagues": "Open leagues", "closed_leagues": "Closed leagues", "joined_league": "Joined league: {name}", "matchday": "Matchday:", "table_length": "Table length:", "search_leagues": "Search leagues", "placeholders": {"league_code": "Enter league code"}, "season": "Season:", "tabs": {"create": "Create league", "join": "Join league", "standings": "Standings"}, "standings": {"title": "{competitionName} Standings", "position": "Position", "name": "Name", "perfect": "Perfect", "correct": "Correct", "wrong": "Wrong", "points": "Points", "search": "Search", "page_size": "Page size", "previous": "Previous", "next": "Next", "no_results": "No results found", "showing_results": "Showing {from} to {to} of {total} results", "error": {"title": "Error loading standings", "description": "There was an error loading the standings. Please try again later."}}, "favorite_team": "Favorite Team", "quick_select_favorite_team": "Quickly select your favorite team for this competition", "stages": {"playoffs": "Playoffs {leg, plural, =1 {first} =2 {second} other {}} leg", "last_16": "Last 16 {leg, plural, =1 {first} =2 {second} other {}} leg", "quarter_finals": "Quarter Finals {leg, plural, =1 {first} =2 {second} other {}} leg", "semi_finals": "Semi Finals {leg, plural, =1 {first} =2 {second} other {}} leg", "final": "Final"}}, "competitions_page": {"card": {"country": "{country}", "participants": "Participants {participants}", "title": "Available Competitions"}, "header": "Competitions"}, "general": {"competitions": "Competitions", "join": "Join", "apply": "Apply", "title": "Bragging Rights", "user": "User", "home": "Home", "leagues": "Leagues", "application_status": {"pending": "Pending", "accepted": "Accepted", "rejected": "Rejected"}}, "league_page": {"errors": {"query_leagues_error": "Something went wrong when fetching your leagues, please try again later", "score_required": "Score is required", "fetch_applications": "Failed to fetch applications. Please try again.", "update_user_applications": "Failed to update user application. Please try again."}, "info": {"closed": "Closed", "league_details": "League details", "open": "Open", "owner": "Owner", "participants": "Participants", "status": "Status", "total_players": "Total players", "leaving_league": {"leave_league": "Leave League", "confirm_leave_league": "Confirm Leave League", "leave_league_description": "Are you sure you want to leave {name}? This action cannot be undone.", "cancel": "Cancel", "leave": "Leave", "leaving": "Leaving...", "left_league_success": "You have successfully left {name}", "left_league_error": "An error occurred while trying to leave the league"}, "scoring_starts_from": "Scoring starts from: ", "matchday": "Matchday {matchday}", "change_starting_matchday_form": {"change_starting_matchday": "Change starting matchday", "change_starting_matchday_description": "Set the matchday from which scoring will start for this league.", "new_starting_matchday": "New Starting Matchday", "updating": "Updating...", "update": "Update", "starting_matchday_updated": "Starting matchday updated successfully", "error_updating_starting_matchday": "Error updating starting matchday"}}, "invite_player": {"buttons": {"copy_invitation_button_text_default": "Copy invitation text", "copy_invite_link_button_text_default": "Copy invite link", "invite_players": "Invite players", "send_email": "Send an email invite"}, "copied": "<PERSON>pied", "copy_invite_link_text": "The invite link allows you to share a unique link with others to invite them to join the league. When they click on the link, they will be directed to the league's registration page where they can sign up and join the league.", "invitation_email_text": "If you are using a web-based email application such as Gmail, you can simply copy and paste the following text:", "invitation_text": {"header": "Welcome to Bragging Rights", "paragraphs": {"1st": "Join my league, ''{leagueName}'' and compete against other players!", "2nd": "League Code: {leagueId}", "3rd": "Looking forward to playing against you this season! Prepare to lose!"}}, "invite_players_to_join_league": "Invite players to join {leagueName}", "link": "Link", "send_email_invite_text": "Send an email link will open your default email program and create a new email. If you don't have a default email program set up on your computer, clicking the link may not do anything or you may be asked to configure an email program."}, "league_table_caption": "User standings for {leagueName}", "match_card": {"ally": {"score_input": {"away": "Away score input for {teamName}", "home": "Home score input for {teamName}"}}, "pts": "pts", "scoring": {"correct": "Correct", "perfect": "Perfect", "wrong": "Wrong"}, "vs": "vs", "your_prediction": "Your prediction: {homeScore} - {awayScore}", "error": {"incomplete_prediction": "Please enter both scores"}, "matchday": "Matchday {matchday}", "prediction_closed": "Prediction closed", "prediction_closed_tooltip": "Predictions for matches close 11 minutes before kickoff", "prediction_locked": "Prediction locked", "prediction_locked_tooltip": "Your prediction is locked as the match is about to start", "not_started": "Not started", "ongoing": "Ongoing", "saved": "Saved", "unsaved": "Unsaved", "no_data": "No data"}, "predict": {"submit": "Submit", "submitting": "Submitting...", "current_matchday": "Current Matchday: {matchday}", "toast": {"error": "Error submitting predictions", "success_create": "Round prediction created successfully", "success_update": "Round prediction updated successfully", "success_batch_create": "Predictions successfully saved for all matchdays", "partial_success_batch_create": "Saved predictions for {succeeded} matchdays, failed for {failed}", "batch_error": "Failed to save prediction", "error_incomplete_predictions": "Please complete both scores for any match you want to predict."}, "loading": {"round": "Loading round matches..."}, "errors": {"failed_to_fetch_match_data": "Failed to fetch match data. Please try again later"}, "season_over": {"title": "Season is over", "message": "The season has ended. No matches are available for prediction.", "view_historical": "View historical standings", "wait_for_new_season": "Please wait for the new season to start making predictions again."}}, "results": {"buttons": {"next": "Next Matchday", "previous": "Previous Matchday"}, "errors": {"failed_to_fetch_match_data": "Failed to fetch match data. Please try again later", "no_changes": "No changes have been made", "something_went_wrong": "Something went wrong, please try again later"}, "loading": {"competition": "Loading competition data...", "round": "Loading round matches..."}, "round_selector": {"ally": {"matchday": "Select matchday {matchday}", "selector": "Select a round to display"}, "matchday": "Matchday: {matchday}", "league_stage": "Matchday: {matchday}", "playoffs": "Playoffs {leg}", "last_16": "Last 16 {leg}", "quarter_finals": "Quarter Finals {leg}", "semi_finals": "Semi Finals {leg}", "first_leg": "First Leg", "second_leg": "Second Leg", "final": "Final", "placeholder": "Select a round to display"}}, "table": {"correct": "Correct", "name": "Name", "next": "Next", "perfect": "Perfect", "points": "Points", "position": "Position", "previous": "Previous", "search": "Search", "title": "{leagueName} League Table", "wrong": "Wrong", "hidden_standings": "Standings are hidden by the league owner.", "creator_controlled_banner": "Standings reflect only published rounds per creator settings.", "round_selector": {"all": "All", "matchday": "Matchday {matchday}", "placeholder": "Select round"}, "round_scores": {"title": "Round scores - Matchday {matchday}", "name": "Name", "points": "Points", "hidden": "Round scores are hidden by the league owner.", "error": "Failed to load round scores."}, "error": {"title": "Error loading standings", "description": "There was an error loading the standings. Please try again later.", "refresh": "Refresh"}}, "showcase": {"title": "Round Showcase", "description": "Showcase top performers for a round before publishing.", "round_selector": "Select round", "matchday": "Matchday {matchday}", "publish_round": "Publish round", "publishing": "Publishing...", "published": "Published round {roundNumber}", "select_user": "Select user", "add_to_showcase": "Add to showcase", "limit_reached": "Showcase limit reached", "no_showcased": "No showcased users for this round yet.", "remove": "Remove", "round_publication": {"section_title": "Round Publication Control", "unpublished": "Unpublished Rounds", "publish": "Publish", "publish_all": "Publish All", "confirm_bulk": "Are you sure you want to publish all unpublished rounds?", "current_round": "Current Round", "in_progress": "In Progress", "published": "Published", "unpublished_badge": "Unpublished"}, "management": {"completed_on": "Completed {date}", "participants": "{count} participants"}, "hidden": "Round scores are hidden by the league owner.", "error": "Failed to load round scores.", "revealed_count": "Revealed {count}/{total}", "show_all": "Show all", "reveal_button": "Reveal score", "points_label": "{points} points", "revealed": "Revealed", "no_scores": "No scores available.", "highlight_note": "Top {count} highlighted", "league_showcased_title": "League-wide showcased users", "no_league_showcased": "No league-level showcased users configured.", "open_scores_presenter": "Open scores presenter", "open_showcased_presenter": "Open showcased presenter", "popup_blocked": "Popup blocked! Please allow popups for this site.", "reset": "Reset", "next": "Next", "stop": "Stop", "auto_reveal": "Auto reveal", "controls": "Controls", "presenters": "Presenter <PERSON>", "scores_presenter": "Scores Presenter", "showcased_presenter": "Showcased Presenter", "one_by_one": "One by one", "open_window": "Open window", "scores_presenter_title": "Score Presenter - Matchday {roundNumber}", "showcased_presenter_title": "League Showcased - Matchday {roundNumber}"}, "join": {"joined_league": "Joined league: {name}", "card": {"title": "Join {leagueName}", "description": "You've been invited to join this league", "content": {"details": "League Details:", "owner": "Owner: {owner}", "competition": "Competition: {competitionName}", "total_players": "Total players: {totalPlayers}"}, "button": {"text": "Join league", "loading": "Joining league..."}}, "errors": {"title": "Error", "joining_league": "An error occurred while joining the league", "connect_youtube_first": "Connect your YouTube account first to join subscriber-only leagues.", "elevate_scope_required": "We need additional read-only YouTube access to verify subscriptions. Click Connect to continue.", "already_a_member": "You are already a member of this league", "max_leagues_reached": "Maximum leagues reached"}}, "apply": {"card": {"description": "You have been invited to a closed league. To join, you need to submit an application for approval by the league owner.", "button": {"text": "Apply to league", "loading": "Applying..."}}, "applied_to_league": "Applied to league: {name}", "application_ongoing": "Your application is currently under review"}, "applications": {"title": "League Applications", "no_applications": "There are no pending applications.", "user_accepted": "User accepted successfully.", "user_rejected": "User rejected successfully.", "accept": "Accept", "reject": "Reject", "table": {"name": "Applicant Name", "created_at": "Applied On", "status": "Status", "action": "Actions"}, "status": {"pending": "Pending", "accepted": "Accepted", "rejected": "Rejected"}, "reject_dialog": {"title": "Reject Application", "description": "Are you sure you want to reject this application?", "cancel": "Cancel", "reject": "Reject", "rejecting": "Rejecting..."}}, "settings": {"title": "League Settings", "no_access_title": "Settings", "no_access": "You do not have permission to customize this league.", "tabs": {"header": "Header", "branding": "Branding", "colors": "Colors", "config": "Configuration"}, "buttons": {"save": "Save", "saving": "Saving...", "reset": "Reset"}, "header": {"header_text": "Header text", "header_text_placeholder": "Add a headline for your league page", "font": "Header font", "placement": "Header placement", "font_size": "Header font size", "alignment": "Header alignment", "logo_side": "Logo side", "placements": {"navbar": "In the navbar", "above_tabs": "Above tabs", "below_tabs": "Below tabs"}}, "common": {"left": "Left", "center": "Center", "right": "Right"}, "branding": {"upload_logo": "Upload logo", "remove_logo": "Remove logo", "logo_position": "Logo position", "positions": {"left": "Left", "right": "Right"}, "logo_size": "Logo size", "sizes": {"small": "Small", "medium": "Medium", "large": "Large"}, "logo_url": "Logo URL", "no_logo": "No logo", "logo_alt": "League logo"}, "colors": {"primary": "Primary", "secondary": "Secondary", "primary_text": "Primary text", "secondary_text": "Secondary text", "accent": "Accent", "accent_text": "Accent text", "destructive": "Destructive", "destructive_text": "Destructive text", "highlight": "Highlight", "highlight_text": "Highlight text", "muted": "Muted", "muted_text": "Muted text", "navbar_border": "Navbar border", "apply_preset": "Apply {preset}", "reset_defaults": "Reset to defaults"}, "config": {"scoring_starts_from": "Scoring starts from", "matchday": "Matchday {matchday}"}, "visibility": {"title": "Visibility", "standings_visible": "Standings visible", "round_scores_visible": "Round scores visible", "scoring_visibility_mode": "Scoring visibility", "scoring_always_visible": "Always visible", "scoring_creator_controlled": "Creator controlled", "default_visible_for_new_rounds": "Default visible for new rounds", "showcase_user_limit": "Showcase user limit", "showcase_specific_users": "Showcase specific users", "search_users": "Search users", "select_user": "Select user", "add_user": "Add", "no_showcased": "No showcased users for current round.", "remove": "Remove", "manage_showcase_button": "Manage Showcase", "manage_showcase_title": "Manage Showcase", "manage_showcase_description": "Add or remove showcased users for the current round.", "selected_count": "Selected: {count}", "confirm_remove": "Remove from showcase?", "current_showcased": "Current showcased users"}, "preview": {"header_area": "Header area", "header_placeholder": "Your custom header", "active_tab": "Active", "inactive_tab": "Inactive"}}}, "leagues_layout": {"tabs": {"info": "Info", "invite_players": "Invite players", "predict": "Predict", "results": "Results", "table": "Table", "applications": "Applications", "showcase": "Round Showcase", "settings": "Settings"}, "error": {"user_or_league_not_found": "User or league not found"}}, "navbar": {"loading": "Loading...", "authentication": {"logged_in_as": "Logged in as: ", "login": "<PERSON><PERSON>", "logout": "Log out", "signup": "Signup"}, "languages": {"en": "English", "is": "Icelandic", "sv": "Swedish", "selector_placeholder": "Language"}, "links": {"user": "User", "competitions": "Competitions"}}, "root_page": {"description": "Join Bragging Rights and test your football prediction skills across top leagues!", "form": {"placeholder": "Enter your email", "submit": "Sign Up Now", "title": "Ready to prove your prediction skills?"}, "header": "Predict. Compete. Win."}, "user_page": {"leagues_card": {"title": "Leagues", "no_leagues": "You are not in any leagues yet. Join a league to start competing.", "competition_page": "Go to competition page"}, "stats_card": {"average_score": "Average Score in a round:", "highest_perfect_score": "Most 'Perfect Score' in a round:", "highest_score": "Highest Score in a round:", "total_points": "Total Points:", "total_predictions": "Total Predictions:", "prediction_accuracy": "Prediction Accuracy:", "perfect_predictions": "Perfect Predictions:", "correct_predictions": "Correct Predictions:", "incorrect_predictions": "Incorrect Predictions:", "title": "Stats"}, "user_card": {"edit_button": "Edit user", "email": "Email: {email}", "location": "Location: {location}", "change_favorite_team": "Change Favorite Teams", "favorite_teams": "Favorite Teams", "select_your_favorite_teams": "Select Your Favorite Teams", "select_favorite_team": "Select Your Favorite Team", "no_competitions": "You are not in any leagues yet. Join a league to select your favorite team.", "no_favorite_teams": "No favorite teams selected yet."}, "youtube_integration": {"manage": "Manage YouTube Integration"}, "team_standings": {"page_title": "Favorite Team Standings", "page_description": "View how users are performing in predictions for your favorite teams.", "favorite_teams_standings": "Favorite Team Standings", "favorite_teams_description": "See how users are performing in predictions for your favorite teams.", "title": "{teamName} Fan Standings", "title_with_competition": "{teamName} Fan Standings - {competitionName}", "position": "Position", "name": "Name", "perfect": "Perfect", "correct": "Correct", "wrong": "Wrong", "points": "Points", "search": "Search", "page_size": "Page size", "previous": "Previous", "next": "Next", "no_results": "No results found", "showing_results": "Showing {from} to {to} of {total} results", "error": {"title": "Error loading standings", "description": "There was an error loading the standings. Please try again later."}, "no_favorite_teams": {"title": "No Favorite Teams", "description": "You haven't selected any favorite teams yet. Go to your profile and select your favorite teams to see their standings."}}}, "footer": {"copyright": "© {year} BragRights Football. All rights reserved.", "contact": "Contact:", "terms": "Terms of Service", "privacy": "Privacy Policy"}, "not_found_page": {"title": "Page Not Found", "description": "Sorry, the page you are looking for does not exist.", "auto_redirect": "You will be redirected to the homepage in {seconds} seconds.", "go_back": "Go back", "user_page": "Go to User page"}, "landing_page": {"hero": {"title": "Bragging Rights", "subtitle": "Football Prediction Platform for Premier Leagues Across Europe", "description": "Compete for", "start_predicting": "Start Predicting Now", "login": "<PERSON><PERSON>"}, "competitions": {"title": "Available Competitions", "premier_league": "Predict matches from England's top flight competition", "champions_league": "Test your knowledge in Europe's elite competition", "besta_deild": "Follow Iceland's premier football league", "allsvenskan": "Experience Sweden's highest football division", "premier_league_alt": "Premier league emblem", "champions_league_alt": "Champions league emblem", "besta_deild_alt": "Besta deildin emblem", "allsvenskan_alt": "Allsvenskan emblem"}, "how_it_works": {"title": "How It Works", "scoring_title": "Scoring System", "scoring_description": "Earn points based on the accuracy of your predictions:", "scoring_points": {"perfect": "Perfect Score: 3 points", "correct": "Correct Outcome: 1 point", "wrong": "Incorrect: 0 points"}, "leagues_title": "League Participation", "leagues_description": "You can join up to 5 different leagues to compete with friends, colleagues, or other football enthusiasts."}, "cta": {"title": "Ready to Test Your Prediction Skills?", "create_account": "Sign Up Now"}, "creators": {"title": "Creators", "left": {"title": "Host leagues for your YouTube audience", "bullets": {"sub_requirement": "Requires 100+ subscribers", "invite": "Invite your community and reward engagement", "read_only": "Read-only YouTube access – no posting"}}, "right": {"title": "How it works", "bullets": {"connect": "Connect your channel (Google OAuth)", "verify": "We verify your subscriber count", "create": "Create a league in minutes"}}}}, "terms": {"title": "Terms of Service", "last_updated": "Last updated: {date}", "back_home_button": "Back to Home", "introduction": {"title": "Introduction", "description": "Welcome to BragRights. These Terms of Service govern your use of our website and services. By accessing or using our platform, you agree to be bound by these terms.", "agreement": "Please read these terms carefully before using our services. If you do not agree with any part of these terms, you may not use our services."}, "eligibility": {"title": "Eligibility", "description": "To use our services, you must meet the following eligibility requirements:", "list": {"age": "You must be at least 13 years of age", "account": "You must create an account with accurate information", "information": "You must provide and maintain accurate, complete, and up-to-date information"}}, "user_accounts": {"title": "User Accounts", "description": "When you create an account with us, you are responsible for maintaining the security of your account and the activities that occur under your account.", "list": {"responsibility": "You are responsible for all activities that occur under your account", "security": "You must immediately notify us of any unauthorized use of your account", "unauthorized": "You must not share your account credentials with any third party", "termination": "We reserve the right to terminate or suspend your account at our discretion"}}, "user_conduct": {"title": "User Conduct", "description": "When using our services, you agree not to engage in any of the following prohibited activities:", "list": {"illegal": "Use our services for any illegal purpose or in violation of any laws", "harmful": "Post or transmit harmful, offensive, or inappropriate content", "impersonation": "Impersonate any person or entity or misrepresent your affiliation", "spam": "Engage in any spamming, advertising, or solicitation activities", "interference": "Interfere with or disrupt the operation of our services", "automated": "Use any automated means to access or use our services"}}, "intellectual_property": {"title": "Intellectual Property", "description": "Our platform and its original content, features, and functionality are owned by BragRights and are protected by international copyright, trademark, and other intellectual property laws.", "ownership": "You may not copy, modify, distribute, sell, or lease any part of our services without our explicit permission."}, "third_party": {"title": "Third-Party Services", "description": "Our services may contain links to third-party websites or services that are not owned or controlled by BragRights.", "responsibility": "We have no control over, and assume no responsibility for, the content, privacy policies, or practices of any third-party websites or services."}, "disclaimer": {"title": "Disclaimer of Warranties", "description": "Our services are provided on an 'as is' and 'as available' basis without any warranties of any kind.", "as_is": "We do not guarantee that our services will be uninterrupted, timely, secure, or error-free."}, "limitation": {"title": "Limitation of Liability", "description": "In no event shall BragRights, its directors, employees, partners, agents, suppliers, or affiliates be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses."}, "changes": {"title": "Changes to Terms", "description": "We reserve the right to modify or replace these terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect."}, "contact_us": {"title": "Contact Us", "description": "If you have any questions about these Terms of Service, please contact us at:", "name": "BragRights", "email": "Email: {email}"}}, "privacy": {"title": "Privacy Policy", "last_updated": "Last updated: {date}", "back_home_button": "Back to Home", "introduction": {"title": "Introduction", "description": "At BragRights, we respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you visit our website and tell you about your privacy rights and how the law protects you.", "applies_to": "This privacy policy applies to all users of our platform, including those who register to use our services, participate in competitions, or simply browse our website."}, "information_collection": {"title": "Information We Collect", "personal_information": {"title": "Personal Information", "description": "We may collect the following types of personal information:", "list": {"name_and_contact_details": "Name and contact details (email address, location)", "account_credentials": "Account credentials (username)", "performance_data": "Performance data (scores, rankings, league participation)", "communication_preferences": "Communication preferences"}}, "third_party_services": {"title": "Third-Party Services", "description": "Our site uses certain third-party services:", "list": {"analytics": "Google AdSense: Displays advertisements which may use cookies to show relevant ads", "framework": "Next.js: Our website framework may collect anonymous usage data"}}}, "use_of_information": {"title": "How We Use Your Information", "description": "We use the information we collect for various purposes, including:", "list": {"providing_platform": "Providing, operating, and maintaining our platform", "improving_platform": "Improving, personalizing, and expanding our platform", "understanding_usage": "Understanding and analyzing how you use our platform", "developing_features": "Developing new products, services, features, and functionality", "communicating_updates": "Communicating with you about updates, security alerts, and support", "sending_marketing": "Sending you marketing and promotional communications (with your consent)", "preventing_fraud": "Finding and preventing fraud", "compliance": "For compliance purposes, including enforcing our Terms of Service"}}, "data_sharing": {"title": "Data Sharing and Disclosure", "intro": "At BragRights, we are committed to protecting your privacy. We do not sell or trade your personal information. We share, transfer, or disclose Google/YouTube user data only as described below.", "when_we_share": {"title": "When We Share Your Data", "service_providers": {"google": "Google Services: We use Google OAuth for authentication and YouTube API services to verify channel ownership and subscriptions. Your YouTube data is accessed only to provide our core functionality.", "platform": "Platform and hosting: Our web platform and hosting infrastructure may process logs (e.g., IP address, user-agent) for security and performance. This does not include your YouTube subscription details.", "adsense": "Google AdSense: We display advertisements via Google AdSense, which may use cookies to show relevant ads. Ad serving is governed by Google's policies."}, "oauth_credentials": "We securely store encrypted OAuth credentials to maintain your YouTube connection and provide ongoing verification services. These credentials allow us to verify your channel ownership and subscription status as needed for platform features without requiring repeated authorization."}, "legal": {"title": "Legal Requirements", "items": {"comply": "Comply with legal obligations, court orders, or government requests", "defend": "Protect and defend our rights or property", "safety": "Protect the safety of our users or the public", "investigate": "Prevent or investigate possible wrongdoing"}}, "business_transfers": {"title": "Business Transfers", "body": "If we are involved in a merger, acquisition, or sale of assets, your information may be transferred as part of the transaction. We will provide notice before your personal information becomes subject to a different privacy policy."}, "platform_sharing": {"title": "What We Share Within Our Platform", "items": {"channel_username": "Your YouTube channel name is used to generate or display your username/identity on BragRights in creator contexts.", "auto_username_from_youtube": "When you sign up using YouTube OAuth, we automatically use your YouTube username to generate your username on our platform. This username becomes visible to all users across our platform in league tables, standings, and social features, regardless of whether you are in creator mode.", "username_visible": "Your YouTube-derived username is visible to all users on our platform in league contexts, leaderboards, and public areas.", "league_name_usage": "When you create a subscriber-only league, we pre-fill the league name field with your YouTube channel name. You can modify this before creating the league.", "owner_name_on_pages": "League pages show channel/owner context for creator leagues.", "basic_public_info": "Only basic, publicly available channel information is shown to other users."}}, "visibility_scope": {"title": "Visibility Scope", "items": {"username_visible_scope": "Your YouTube-derived username is visible to all users on our platform in league contexts, leaderboards, and public sections.", "league_creator_info_visible": "Channel information for league creators is visible to members of those leagues and to users on join flows.", "limited_to_public_info": "The information shown is limited to what is already publicly available on your YouTube channel."}}, "data_storage": {"title": "Data Storage", "items": {"channel_id_name": "Channel ID and channel name (for identification and display)", "subscriber_avatar": "Subscriber count and avatar URL (for creator mode eligibility and profile display)", "oauth_credentials": "OAuth access credentials (encrypted, for ongoing verification)", "verification_timestamps": "Channel verification status and connection timestamps"}, "note": "This data is retained in our secure database to provide ongoing platform functionality."}, "not_share": {"title": "What We Don't Share", "items": {"no_sale": "We do not sell your personal information to third parties.", "no_share_sub_status": "We do not share your individual YouTube subscription status or the list of channels you subscribe to with other users.", "no_marketing_brokers": "We do not transfer your data to marketing companies or data brokers.", "no_disclose_performance": "We do not disclose your league performance or prediction data to external parties without your consent.", "no_private_beyond_needed": "We do not share private YouTube data beyond what is necessary for core features."}}, "youtube_specific": {"title": "YouTube Data Specific Disclosures", "items": {"verify_ownership": "Verifying channel ownership for content creator features.", "confirm_subscriptions": "Confirming subscriptions for subscriber-only leagues.", "display_channel_info": "Displaying your channel information (such as channel name) within our platform in creator contexts.", "background_verification": "We periodically verify YouTube subscriptions for users in subscriber-only leagues through automated background processes to ensure league integrity and access control. These processes run without user interaction to maintain accurate league membership."}, "notice": "This data is not shared with other users beyond what you explicitly allow (for example, league names using your channel name)."}, "retention": {"title": "Data Retention", "body": "YouTube data retention policy: Data is retained while your YouTube account remains connected to BragRights. Upon disconnection, YouTube identifiers are removed within 7 days. Upon account deletion, all associated YouTube data is permanently deleted within 30 days. You can revoke access through your Google Account settings at myaccount.google.com/permissions."}, "google_verification": {"title": "Google User Data Sharing Disclosure", "body": "BragRights does not share, transfer, or disclose Google user data to third parties except as explicitly described in this privacy policy. All use of Google user data adheres to Google's Limited Use Policy and is restricted to providing and improving our platform features."}}, "cookies_and_tracking": {"title": "Cookies and Tracking Technologies", "intro": "We use the following types of cookies:", "essential": {"title": "Essential Cookies", "items": {"bragging_rights_token": "BraggingRightsToken: JWT authentication token for secure user sessions and API access (expires when you log out or session ends)", "locale": "NEXT_LOCALE: To remember your language preference (persistent until manually changed, not used on Privacy Policy or Terms of Service pages)"}}, "third_party": {"title": "Third-Party Cookies", "items": {"adsense": "Google AdSense: For displaying relevant advertisements which may use cookies to show relevant ads (managed by Google's policies)"}, "note": "Note: When you connect your YouTube account, your authentication credentials are securely stored in encrypted form within our authentication system, not as separate cookies."}}, "childrens_privacy": {"title": "Children's Privacy", "description": "Our platform is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we become aware that we have collected personal information from a child under 13, we will take steps to delete such information."}, "changes_to_policy": {"title": "Changes to This Policy", "description": "We may update our privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page and updating the ''Last updated'' date at the top of this policy. You are advised to review this privacy policy periodically for any changes."}, "contact_us": {"title": "Contact Us", "description": "If you have any questions about this privacy policy or our privacy practices, please contact us at:", "name": "BragRights", "email": "Email: {email}"}, "youtube_api_services": {"title": "YouTube API Services", "uses_api_services": "This app uses YouTube API Services.", "revoke": "You can revoke access at any time in your Google Account:", "see": "See", "and": "and", "youtube_tos": "YouTube Terms of Service", "google_privacy": "Google Privacy Policy"}}, "youtube": {"auth": {"signup": "Sign up with YouTube", "login": "Log in with YouTube", "or_continue_with": "or continue with", "disconnect": "Disconnect YouTube"}, "profile": {"title": "YouTube Integration", "description": "Connect your YouTube account. To access YouTube features, upgrade your permissions to include read-only access to your YouTube data when prompted.", "loading": "Loading YouTube account information...", "connected_account": {"title": "Connected Account", "channel": "Channel: {channelName}", "channel_id": "Channel ID: {channelId}"}, "creator_mode": {"toggle": "Content Creator Mode", "description": "As a content creator, you can create subscriber-only leagues that require users to subscribe to your YouTube channel.", "requirements": "Requires a minimum of {minSubscribers} subscribers to enable.", "needs_scope": "To use YouTube features, you need to elevate your scope. This will allow us to verify your subscriptions.", "visibility_notice": "When creator mode is enabled, your YouTube channel name may be shown as your league name and a link to your channel may be presented to users joining your subscriber-only leagues."}, "not_connected": {"description": "Connect your YouTube account to join subscriber-only leagues or create your own leagues that require YouTube subscription."}, "buttons": {"connect": "Connect YouTube", "disconnect": "Disconnect YouTube", "link_youtube": "Link YouTube account", "enable_creator_mode": "Enable Content Creator Mode", "manage_youtube": "Manage YouTube Integration", "elevate_scope": "Elevate <PERSON>", "connect_yt": "Connect YouTube account", "grant_access": "Grant YouTube Access", "open_channel": "Open your YouTube Channel", "visit_youtube": "Visit YouTube"}, "toast": {"connected": "YouTube account has been successfully connected.", "disconnected": "Your YouTube account has been disconnected.", "disconnect_error": "Failed to disconnect YouTube account.", "creator_enabled": "Creator Status Enabled. You can now create YouTube subscriber-only leagues.", "creator_disabled": "Creator Status Disabled. You can no longer create YouTube subscriber-only leagues.", "creator_error": "Failed to update creator status.", "insufficient_subscribers": "Insufficient subscribers: You have {current} subscribers, but {required} are required to enable content creator mode.", "already_connected": "This YouTube channel is already connected to another account ({email}). You need to disconnect it from that account first."}, "errors": {"already_connected": "YouTube Channel Already Connected", "already_connected_description": "This YouTube channel is already connected to {email}. You need to disconnect it from that account first before connecting it to yours."}, "elevate_scope": {"title": "Grant YouTube access", "description": "To access YouTube features, upgrade your permissions to include read-only access to your YouTube data."}, "compliance": {"uses_api_services": "This app uses YouTube API Services.", "revoke": "You can revoke access at any time in your Google Account:", "see": "See", "and": "and", "youtube_tos": "YouTube Terms of Service", "google_privacy": "Google Privacy Policy"}, "confirm": {"disconnect_title": "Disconnect Google/YouTube access?", "disconnect_body": "This will remove stored YouTube identifiers from your BragRights account and revoke our access to your YouTube data. You can also revoke access in your Google Account settings.", "cancel": "Cancel", "disconnect": "Disconnect"}}, "subscription": {"title": "YouTube Subscription Required", "description": "This league requires a YouTube subscription to join.", "channel_info": "To join this league, you need to subscribe to the creator's YouTube channel:", "channel_name": "{channelName}", "channel_id": "Channel ID: {channelId}", "buttons": {"connect": "Connect YouTube", "open_channel": "Open YouTube Channel", "verify": "Verify Subscription"}}, "subscriber_only_modal": {"title": "Subscriber-only League", "description": "This league requires a subscription to YouTube channel ''{channelName}''. <PERSON> read-only access so we can verify your YouTube subscriptions.", "explain_verify": "We require read-only access to your YouTube subscriptions to verify that you are subscribed to the creator's channel.", "channel_info": "Required YouTube subscription:", "buttons": {"cancel": "Cancel", "view_creator_channel": "View Channel: {channelName}"}, "aria": {"league_name": "League name: {name}"}, "privacy_readonly": "We only request read-only access and will never post to your account. You can revoke access anytime in your Google Account settings."}, "league": {"subscriber_only": "Subscriber-only League", "requires_subscription": "This league requires subscribers to your YouTube channel."}}}