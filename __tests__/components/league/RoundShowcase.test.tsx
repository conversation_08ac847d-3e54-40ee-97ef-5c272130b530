import { fireEvent, render, screen, waitFor } from "@testing-library/react";

import { RoundShowcase } from "@/src/components/league/showcase/RoundShowcase";
import { createQueryClientWrapper } from "@/__tests__/test-utils";

jest.mock("next-intl", () => ({
  useTranslations: () => (key: string, values?: Record<string, any>) =>
    typeof values?.default === "string" ? values.default : key,
}));

jest.mock("@/src/hooks/use-league-data", () => ({
  useLeagueData: () => ({
    data: {
      id: 1,
      showcase_user_limit: 2,
      users: [
        { id: 10, username: "<PERSON>" },
        { id: 11, username: "<PERSON>" },
      ],
    },
    isLoading: false,
  }),
}));

jest.mock("@/src/hooks/use-round-visibility-info", () => ({
  useRoundVisibilityInfo: () => ({
    data: {
      league_id: 1,
      season_id: 1,
      published_round_numbers: [],
      unpublished_round_numbers: [3],
      rounds_with_showcase_data: [3],
      per_round: [
        {
          round_number: 3,
          visible: false,
          finished: true,
          top_performers_available: true,
          league_showcased_users_available: true,
          view_permission: "owner_showcase",
        },
      ],
    },
    isLoading: false,
  }),
}));

jest.mock("@/src/hooks/use-round-visibility", () => ({
  usePublishRound: () => ({
    mutateAsync: jest.fn().mockResolvedValue({}),
    isPending: false,
  }),
}));

jest.mock("@/src/hooks/use-league-showcased-users", () => ({
  useLeagueShowcasedUsers: () => ({
    data: {
      league_id: 1,
      round_number: 3,
      showcased_user_ids: [10],
      scores: [
        {
          user_id: 10,
          username: "Ann",
          round_number: 3,
          total_points: 7,
          breakdown: [],
          perfect: 1,
          incorrect: 0,
          correct: 2,
          position: 1,
        },
      ],
    },
    isLoading: false,
  }),
}));

jest.mock("@/src/hooks/use-league-round-scores", () => ({
  useLeagueRoundScores: () => ({
    data: {
      league_id: 1,
      round_number: 3,
      scores: [
        {
          user_id: 10,
          username: "Ann",
          round_number: 3,
          total_points: 7,
          breakdown: [],
        },
        {
          user_id: 11,
          username: "Bob",
          round_number: 3,
          total_points: 5,
          breakdown: [],
        },
      ],
    },
    isLoading: false,
    error: null,
  }),
}));

describe("RoundShowcase", () => {
  it("renders presenter controls and buttons", async () => {
    render(<RoundShowcase id="1" />, { wrapper: createQueryClientWrapper() });

    // Check for Scores Presenter section
    expect(await screen.findByText(/Scores Presenter/i)).toBeInTheDocument();

    // Check for control buttons (there are multiple "Next" buttons, so use getAllByRole)
    const nextButtons = screen.getAllByRole("button", { name: /Next/i });
    expect(nextButtons.length).toBeGreaterThan(0);

    const oneByOneButtons = screen.getAllByRole("button", {
      name: /One by one/i,
    });
    expect(oneByOneButtons.length).toBeGreaterThan(0);

    const showAllButtons = screen.getAllByRole("button", { name: /Show all/i });
    expect(showAllButtons.length).toBeGreaterThan(0);

    // Check for Showcased Presenter section
    expect(screen.getByText(/Showcased Presenter/i)).toBeInTheDocument();

    // Highlight note based on showcase_user_limit
    expect(screen.getByText(/Top .* highlighted/i)).toBeInTheDocument();
  });

  it("shows publish button for unpublished round", async () => {
    render(<RoundShowcase id="1" />, { wrapper: createQueryClientWrapper() });

    const publishBtn = await screen.findByRole("button", {
      name: /Publish round/i,
    });
    expect(publishBtn).toBeInTheDocument();
  });
});
