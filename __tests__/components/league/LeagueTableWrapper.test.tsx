import { fireEvent, render, screen } from "@testing-library/react";

import LeagueTableWrapper from "@/src/components/league/table/league-table-wrapper";
import { createQueryClientWrapper } from "@/__tests__/test-utils";

jest.mock("next-intl", () => ({
  useTranslations: () => (subkey: string, params?: any) => {
    if (subkey === "round_selector.matchday")
      return `Matchday ${params?.matchday}`;
    if (subkey === "round_scores.title")
      return `Round scores - Matchday ${params?.matchday}`;
    const map: Record<string, string> = {
      hidden_standings: "Standings are hidden by the league owner.",
      "round_selector.all": "All",
      "round_selector.placeholder": "Select round",
      "round_scores.hidden": "Round scores are hidden by the league owner.",
      "round_scores.error": "Failed to load round scores.",
      "error.title": "Error loading standings",
      "error.description":
        "There was an error loading the standings. Please try again later.",
      "error.refresh": "Refresh",
    };
    return map[subkey] || subkey;
  },
}));

jest.mock("@/src/hooks/use-league-data", () => ({
  useLeagueData: jest.fn(),
}));

jest.mock("@/src/hooks/use-league-standings", () => ({
  useLeagueStandings: jest.fn(),
}));

jest.mock("@/src/hooks/use-league-round-scores", () => ({
  useLeagueRoundScores: jest.fn(),
}));

jest.mock("@/src/hooks/use-round-visibility-info", () => ({
  useRoundVisibilityInfo: jest.fn(),
}));

jest.mock("@/src/hooks/use-league-showcased-users", () => ({
  useLeagueShowcasedUsers: jest.fn(),
}));

jest.mock("@/src/hooks/use-current-user", () => ({
  useCurrentUser: jest.fn(),
}));

const { useLeagueData } = jest.requireMock("@/src/hooks/use-league-data");
const { useLeagueStandings } = jest.requireMock(
  "@/src/hooks/use-league-standings"
);
const { useLeagueRoundScores } = jest.requireMock(
  "@/src/hooks/use-league-round-scores"
);
const { useRoundVisibilityInfo } = jest.requireMock(
  "@/src/hooks/use-round-visibility-info"
);
const { useLeagueShowcasedUsers } = jest.requireMock(
  "@/src/hooks/use-league-showcased-users"
);
const { useCurrentUser } = jest.requireMock("@/src/hooks/use-current-user");

describe("LeagueTableWrapper", () => {
  beforeAll(() => {
    // Radix Select calls scrollIntoView
    (Element.prototype as any).scrollIntoView = jest.fn();
  });

  beforeEach(() => {
    jest.resetAllMocks();
  });

  function mockBase({
    standingsVisible = true,
  }: { standingsVisible?: boolean } = {}) {
    useLeagueData.mockReturnValue({
      data: {
        id: "1",
        name: "Test League",
        standings_visible: standingsVisible,
        competition: { current_season: { current_matchday: 3 } },
      },
      isLoading: false,
      isError: false,
    });

    useLeagueStandings.mockReturnValue({
      data: { league: { standings: [] } },
      isLoading: false,
      isError: false,
    });

    useLeagueRoundScores.mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: false,
    });

    useRoundVisibilityInfo.mockReturnValue({
      data: {
        league_id: 1,
        season_id: 1,
        published_round_numbers: [1, 2, 3],
        unpublished_round_numbers: [],
        rounds_with_showcase_data: [],
        per_round: [
          {
            round_number: 1,
            visible: true,
            finished: true,
            top_performers_available: false,
            league_showcased_users_available: false,
            view_permission: "all",
          },
          {
            round_number: 2,
            visible: true,
            finished: true,
            top_performers_available: false,
            league_showcased_users_available: false,
            view_permission: "all",
          },
          {
            round_number: 3,
            visible: true,
            finished: true,
            top_performers_available: false,
            league_showcased_users_available: false,
            view_permission: "all",
          },
        ],
      },
      isLoading: false,
      isError: false,
    });

    useLeagueShowcasedUsers.mockReturnValue({
      data: undefined,
      isLoading: false,
    });

    useCurrentUser.mockReturnValue({
      data: undefined,
      isLoading: false,
    });
  }

  it("shows hidden standings message when standings_visible is false", () => {
    mockBase({ standingsVisible: false });

    render(<LeagueTableWrapper id="1" />, {
      wrapper: createQueryClientWrapper(),
    });

    expect(
      screen.getByText(/Standings are hidden by the league owner\./i)
    ).toBeInTheDocument();
  });

  it("shows round scores hidden message on 403 error when selecting a round", async () => {
    mockBase({ standingsVisible: true });

    useLeagueRoundScores.mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
      error: { error_key: "LEAGUE_ROUND_SCORES_HIDDEN" },
    });

    render(<LeagueTableWrapper id="1" />, {
      wrapper: createQueryClientWrapper(),
    });

    // open select and choose matchday 1
    const trigger = screen.getByRole("combobox");
    fireEvent.click(trigger);
    fireEvent.click(await screen.findByText("Matchday 1"));

    expect(
      await screen.findByText(/Round scores are hidden by the league owner\./i)
    ).toBeInTheDocument();
  });

  it("renders round scores table when data is returned", async () => {
    mockBase({ standingsVisible: true });

    useLeagueRoundScores.mockReturnValue({
      data: {
        league_id: 1,
        round_number: 2,
        scores: [
          {
            user_id: 10,
            username: "Alice",
            round_number: 2,
            total_points: 7,
            breakdown: [],
          },
          {
            user_id: 11,
            username: "Bob",
            round_number: 2,
            total_points: 5,
            breakdown: [],
          },
        ],
      },
      isLoading: false,
      isError: false,
    });

    render(<LeagueTableWrapper id="1" />, {
      wrapper: createQueryClientWrapper(),
    });

    // open select and choose matchday 2
    const trigger = screen.getByRole("combobox");
    fireEvent.click(trigger);
    fireEvent.click(await screen.findByText("Matchday 2"));

    expect(
      await screen.findByText(/Round scores - Matchday 2/)
    ).toBeInTheDocument();
    expect(screen.getByText("Alice")).toBeInTheDocument();
    expect(screen.getByText("Bob")).toBeInTheDocument();
  });
});
