class LeagueRoundVisibility < ApplicationRecord
  belongs_to :league
  belongs_to :season
  belongs_to :made_visible_by_user, class_name: 'User', optional: true

  has_many :showcases, class_name: 'LeagueRoundVisibilityShowcase', dependent: :destroy
  has_many :showcased_users, through: :showcases, source: :user

  validates :round_number, presence: true, numericality: { only_integer: true, greater_than: 0 }

  validates :visible, inclusion: { in: [true, false] }
  validates :made_visible_by_user, presence: true, if: :visible?
  validates :visible_at, presence: true, if: :visible?

  before_update :prevent_unpublish

  private

  def prevent_unpublish
    return unless visible_changed? && visible_change == [true, false]

    errors.add(:visible, 'cannot be set to false once published')
    throw :abort
  end
end
