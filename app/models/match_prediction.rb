# MatchPrediction model
class MatchPrediction < ApplicationRecord
  belongs_to :round_prediction
  belongs_to :match
  belongs_to :user

  validates :points, inclusion: { in: [nil, 0, 1, 3] }

  before_validation :set_user_from_round_prediction, on: :create

  def calculate_points
    return unless match.status == 'FINISHED'

    unless match.score.present?
      Rails.logger.warn("Missing score for finished match #{match.id}")
      return
    end

    self.points = if match.score.fullTimeHome == home_score && match.score.fullTimeAway == away_score
                    3 # Perfect prediction (exact scores match)
                  elsif match_result == actual_result
                    1 # Correct result (right winner/draw but wrong scores)
                  else
                    0 # Incorrect prediction
                  end

    save!
  end

  private

  def set_user_from_round_prediction
    self.user ||= round_prediction.user
  end

  def match_result
    if home_score > away_score
      'HOME_TEAM'
    elsif home_score < away_score
      'AWAY_TEAM'
    else
      'DRAW'
    end
  end

  def actual_result
    if match.score.fullTimeHome > match.score.fullTimeAway
      'HOME_TEAM'
    elsif match.score.fullTimeHome < match.score.fullTimeAway
      'AWAY_TEAM'
    else
      'DRAW'
    end
  end
end
