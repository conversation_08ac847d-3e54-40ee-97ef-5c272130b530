import { useCallback, useRef } from "react";

import { toast } from "sonner";

interface PresenterWindowOptions {
  title: string;
  content: (data: any) => string;
  width?: number;
  height?: number;
  additionalStyles?: string;
  brandingLogoUrl?: string | null;
  brandingText?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tertiaryColor?: string;
}

export function usePresenterWindow({
  title,
  content,
  width = 800,
  height = 900,
  brandingLogoUrl,
  brandingText,
  primaryColor = "#667eea",
  secondaryColor = "#764ba2",
  tertiaryColor,
}: PresenterWindowOptions) {
  const windowRef = useRef<Window | null>(null);

  // Create gradient background based on colors
  const backgroundGradient = tertiaryColor
    ? `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 50%, ${tertiaryColor} 100%)`
    : `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%)`;

  const open = useCallback(() => {
    // Check if window is already open
    if (windowRef.current && !windowRef.current.closed) {
      windowRef.current.focus();
      return;
    }

    const left = window.screenX + (window.outerWidth - width) / 2;
    const top = window.screenY + (window.outerHeight - height) / 2;

    const newWindow = window.open(
      "",
      `presenter_${Date.now()}`,
      `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
    );

    if (!newWindow) {
      toast.error("Popup blocked! Please allow popups for this site.");
      return;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
              padding: 2rem;
              background: ${backgroundGradient};
              min-height: 100vh;
            }
            .container {
              max-width: 800px;
              margin: 0 auto;
            }
            .header {
              background: white;
              padding: 1.5rem;
              border-radius: 12px;
              margin-bottom: 1.5rem;
              box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .header h1 {
              font-size: 2rem;
              font-weight: 700;
              color: #1a202c;
              margin-bottom: 0.5rem;
            }
            .branding {
              display: flex;
              align-items: center;
              gap: 1rem;
              margin-bottom: 1rem;
              padding-bottom: 1rem;
              border-bottom: 2px solid #e5e7eb;
            }
            .branding-logo {
              height: 48px;
              width: auto;
              object-fit: contain;
            }
            .branding-text {
              font-size: 1.25rem;
              font-weight: 600;
              color: #374151;
            }
            .status {
              display: inline-flex;
              align-items: center;
              gap: 0.5rem;
              background: #e0e7ff;
              padding: 0.5rem 1rem;
              border-radius: 8px;
              font-size: 0.875rem;
              font-weight: 600;
              color: #4338ca;
            }
            .pulse {
              width: 8px;
              height: 8px;
              background: #4338ca;
              border-radius: 50%;
              animation: pulse 2s infinite;
            }
            @keyframes pulse {
              0%, 100% { opacity: 1; }
              50% { opacity: 0.5; }
            }
            .list {
              display: flex;
              flex-direction: column;
              gap: 1rem;
            }
            .item {
              background: white;
              border-radius: 12px;
              padding: 1.5rem;
              box-shadow: 0 4px 6px rgba(0,0,0,0.1);
              display: flex;
              align-items: center;
              gap: 1.5rem;
              transition: all 0.5s ease-out;
              opacity: 0;
              transform: translateY(20px) scale(0.95);
            }
            .item.revealed {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
            .item.highlighted {
              border: 3px solid #f59e0b;
              background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            }
            .rank {
              width: 3.5rem;
              height: 3.5rem;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 700;
              font-size: 1.25rem;
              flex-shrink: 0;
            }
            .rank-1 { background: #eab308; color: white; }
            .rank-2 { background: #9ca3af; color: white; }
            .rank-3 { background: #ea580c; color: white; }
            .rank-other { background: #e5e7eb; color: #374151; }
            .username {
              flex: 1;
              font-size: 1.25rem;
              font-weight: 600;
              color: #1a202c;
            }
            .points {
              text-align: right;
            }
            .points-value {
              font-size: 2.5rem;
              font-weight: 700;
              color: #4338ca;
              line-height: 1;
            }
            .points-label {
              font-size: 0.75rem;
              color: #6b7280;
              text-transform: uppercase;
            }
            .star {
              color: #f59e0b;
              width: 2.5rem;
              height: 2.5rem;
            }
            .top-badge {
              position: absolute;
              top: 0;
              right: 0;
              background: #f59e0b;
              color: white;
              padding: 0.25rem 0.75rem;
              font-size: 0.75rem;
              font-weight: 700;
              border-radius: 0 12px 0 12px;
            }
            .showcased-icon {
              width: 3rem;
              height: 3rem;
              border-radius: 50%;
              background: #e0e7ff;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
            }
            .showcased-icon svg {
              width: 1.5rem;
              height: 1.5rem;
              color: #4338ca;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              ${brandingLogoUrl || brandingText
        ? `
                <div class="branding">
                  ${brandingLogoUrl ? `<img src="${brandingLogoUrl}" alt="League Logo" class="branding-logo" />` : ""}
                  ${brandingText ? `<div class="branding-text">${brandingText}</div>` : ""}
                </div>
              `
        : ""
      }
              <h1 id="title">${title}</h1>
              <div class="status">
                <div class="pulse"></div>
                <span id="status">Waiting...</span>
              </div>
            </div>
            <div id="content" class="list"></div>
          </div>
          <script>
            window.addEventListener('message', (event) => {
              if (event.data.type === 'UPDATE_STATE') {
                updateContent(event.data.data);
              }
            });

            function updateContent(data) {
              const content = document.getElementById('content');
              const status = document.getElementById('status');
              
              if (data.autoRevealActive) {
                status.textContent = 'Auto-revealing...';
              } else {
                const count = Object.values(data.revealed).filter(Boolean).length;
                status.textContent = \`Revealed \${count}/\${data.sortedScores.length}\`;
              }

              ${content("data")}
            }
          </script>
        </body>
      </html>
    `;

    newWindow.document.open();
    newWindow.document.write(htmlContent);
    newWindow.document.close();

    windowRef.current = newWindow;

    // Monitor if window is closed
    const checkClosed = setInterval(() => {
      if (newWindow.closed) {
        clearInterval(checkClosed);
        windowRef.current = null;
      }
    }, 1000);
  }, [
    title,
    content,
    width,
    height,
    brandingLogoUrl,
    brandingText,
    backgroundGradient,
  ]);

  const updateState = useCallback((data: any) => {
    if (windowRef.current && !windowRef.current.closed) {
      windowRef.current.postMessage({ type: "UPDATE_STATE", data }, "*");
    }
  }, []);

  const close = useCallback(() => {
    if (windowRef.current && !windowRef.current.closed) {
      windowRef.current.close();
      windowRef.current = null;
    }
  }, []);

  return {
    open,
    updateState,
    close,
  };
}