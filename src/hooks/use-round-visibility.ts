"use client";

import type {
  LeagueUpdateVisibilitySettings,
  PublishBulkRequest,
  RoundVisibilityListResponse,
  RoundVisibilityResponse,
  ShowcaseState,
  ShowcaseUsersRequest,
} from "@/src/types/round-visibility";
import {
  getDataBeClientSide,
  leagueRoundPublishBulkUrl,
  leagueRoundPublishUrl,
  leagueRoundShowcaseUserUrl,
  leagueRoundShowcaseUsersUrl,
  leagueRoundVisibilityDefaultsUrl,
  leagueRoundVisibilityUrl,
  leaguesUrl,
  modifyDataBe,
} from "../api/be-client-side";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { roundVisibilityInfoKeys } from "./use-round-visibility-info";

export const roundVisibilityKeys = {
  all: (leagueId: string | number) => ["roundVisibility", String(leagueId)] as const,
  list: (leagueId: string | number) => [...roundVisibilityKeys.all(leagueId), "list"] as const,
  round: (
    leagueId: string | number,
    roundNumber: number | string,
  ) => [...roundVisibilityKeys.all(leagueId), "round", roundNumber] as const,
};

export function useRoundVisibilityList(leagueId: string | number | undefined, seasonId?: number) {
  const queryKey = seasonId
    ? [...roundVisibilityKeys.list(leagueId ?? ""), seasonId]
    : roundVisibilityKeys.list(leagueId ?? "")

  return useQuery<RoundVisibilityListResponse>({
    queryKey: queryKey,
    queryFn: () =>
      getDataBeClientSide(
        seasonId
          ? `${leagueRoundVisibilityUrl(leagueId!)}` + `?season_id=${seasonId}`
          : `${leagueRoundVisibilityUrl(leagueId!)}`,
        "getRoundVisibilityList",
      ),
    enabled: !!leagueId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUpdateRoundVisibilityDefaults(leagueId: string | number) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: LeagueUpdateVisibilitySettings) => {
      return modifyDataBe(payload, leagueRoundVisibilityDefaultsUrl(leagueId), "PATCH");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: roundVisibilityKeys.all(leagueId) });
      queryClient.invalidateQueries({ queryKey: ["leagueData", String(leagueId)] });
    },
  });
}

export function usePublishRound(leagueId: string | number) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ roundNumber, seasonId }: { roundNumber: number; seasonId?: number }) => {
      const body = seasonId ? { season_id: seasonId } : {};
      return modifyDataBe(body, leagueRoundPublishUrl(leagueId, roundNumber), "POST");
    },
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: roundVisibilityKeys.all(leagueId) });
        queryClient.invalidateQueries({ queryKey: roundVisibilityInfoKeys.all(leagueId ?? "") });
        queryClient.invalidateQueries({ queryKey: ["leagueStandings", String(leagueId)] });
      }, 500);
    },
  });
}

export function usePublishRoundsBulk(leagueId: string | number) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: PublishBulkRequest) => {
      return modifyDataBe(payload, leagueRoundPublishBulkUrl(leagueId), "POST");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: roundVisibilityKeys.all(leagueId) });
      queryClient.invalidateQueries({ queryKey: ["leagueStandings", String(leagueId)] });
    },
  });
}

export function useAddShowcaseUsers(leagueId: string | number, roundNumber: number) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: ShowcaseUsersRequest) => {
      return modifyDataBe(payload, leagueRoundShowcaseUsersUrl(leagueId, roundNumber), "POST");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: roundVisibilityKeys.round(leagueId, roundNumber) });
      queryClient.invalidateQueries({ queryKey: roundVisibilityKeys.list(leagueId) });
    },
  });
}

export function useRemoveShowcasedUser(
  leagueId: string | number,
  roundNumber: number,
  userId: number,
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async () => {
      return modifyDataBe({}, leagueRoundShowcaseUserUrl(leagueId, roundNumber, userId), "DELETE");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: roundVisibilityKeys.round(leagueId, roundNumber) });
      queryClient.invalidateQueries({ queryKey: roundVisibilityKeys.list(leagueId) });
    },
  });
}

