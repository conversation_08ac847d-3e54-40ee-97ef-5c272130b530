"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>it<PERSON>,
} from "@/src/components/ui/card";
import {
  FEATURE_LEAGUE_CUSTOMIZATION,
  FEATURE_LEAGUE_CUSTOMIZATION_COLORSCHEME,
} from "@/src/constants/league-customization";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/src/components/ui/tabs";
import { useEffect, useMemo, useState } from "react";
import {
  useLeagueCustomization,
  useUpdateLeagueCustomization,
} from "@/src/hooks/useLeagueCustomization";

import { BrandingSection } from "@/src/components/league/settings/BrandingSection";
import { Button } from "@/src/components/ui/button";
import { ColorSchemeSection } from "@/src/components/league/settings/ColorSchemeSection";
import { DEFAULT_LEAGUE_CUSTOMIZATION } from "@/src/constants/league-customization";
import { HeaderCustomization } from "@/src/components/league/settings/HeaderCustomization";
import { LeagueConfiguration } from "@/src/components/league/settings/LeagueConfiguration";
import type { LeagueCustomization } from "@/src/types/league-customization";
// import { LeagueShowcasedUsersManager } from "@/src/components/league/settings/LeagueShowcasedUsersManager";
import { toast } from "sonner";
import { uploadImageToCloudinary } from "@/src/libs/cloudinary";
import { useFeatureCheck } from "@/src/hooks/useFeatureToggle";
import { useLeagueCustomizationLive } from "@/src/components/league/layout/LeagueCustomizationProvider";
import { useLeagueData } from "@/src/hooks/use-league-data";
import { useTranslations } from "next-intl";

interface LeagueSettingsProps {
  id: string;
  defaultCustomization?: LeagueCustomization;
}

// Helper to merge draft changes with server customization
const getMergedCustomization = (
  customization: LeagueCustomization,
  draft: Partial<LeagueCustomization> | null
): LeagueCustomization => {
  if (!draft) return customization;

  return {
    ...customization,
    ...draft,
    colorScheme: {
      ...customization.colorScheme,
      ...(draft.colorScheme || {}),
    },
  };
};

export function LeagueSettings({
  id,
  defaultCustomization,
}: LeagueSettingsProps) {
  const t = useTranslations("league_page.settings");
  const { data: league, isLoading } = useLeagueData(id);
  const { data: customization } = useLeagueCustomization(id);
  const updateMutation = useUpdateLeagueCustomization(id);
  const [draft, setDraft] = useState<Partial<LeagueCustomization> | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const { setLiveDraft } = useLeagueCustomizationLive();

  const defaults = useMemo(() => {
    return defaultCustomization
      ? defaultCustomization
      : DEFAULT_LEAGUE_CUSTOMIZATION;
  }, [defaultCustomization]);

  const LS_KEY = useMemo(
    () => `league-customization-draft-${id}` as const,
    [id]
  );

  // Load draft from localStorage on mount
  useEffect(() => {
    if (customization) {
      try {
        const saved =
          typeof window !== "undefined"
            ? window.localStorage.getItem(LS_KEY)
            : null;

        if (saved) {
          const parsedSaved = JSON.parse(saved);
          // Only set draft if there are actual changes
          if (Object.keys(parsedSaved).length > 0) {
            setDraft(parsedSaved);
          }
        }
      } catch {
        // Ignore localStorage errors
      }
    }
  }, [customization, LS_KEY]);

  // Persist draft to localStorage
  useEffect(() => {
    if (draft && Object.keys(draft).length > 0) {
      try {
        window.localStorage.setItem(LS_KEY, JSON.stringify(draft));
      } catch {}
    } else if (draft === null) {
      // Clear localStorage when draft is reset
      try {
        window.localStorage.removeItem(LS_KEY);
      } catch {}
    }
  }, [draft, LS_KEY]);

  // Calculate if form is dirty (draft has changes)
  const isDirty = useMemo(() => {
    const hasDraft = draft && Object.keys(draft).length > 0;
    return !!hasDraft;
  }, [draft]);

  // Prevent page reload if there are unsaved changes
  useEffect(() => {
    const handler = (e: BeforeUnloadEvent) => {
      if (isDirty) {
        e.preventDefault();
        e.returnValue = "";
      }
    };
    window.addEventListener("beforeunload", handler as any);
    return () => window.removeEventListener("beforeunload", handler as any);
  }, [isDirty]);

  // Helper to clear draft
  const clearDraft = () => {
    setDraft(null);
    try {
      window.localStorage.removeItem(LS_KEY);
    } catch {}
  };

  // Update draft with partial changes
  const updateDraft = (changes: Partial<LeagueCustomization>) => {
    setDraft((currentDraft) => {
      const newDraft = { ...(currentDraft || {}), ...changes };

      // If colorScheme changes, merge them properly
      if (changes.colorScheme && currentDraft?.colorScheme) {
        newDraft.colorScheme = {
          ...currentDraft.colorScheme,
          ...changes.colorScheme,
        };
      }

      return newDraft;
    });
  };

  // Get the current effective customization (server + draft changes)
  const currentCustomization = useMemo(() => {
    if (!customization) return null;
    return getMergedCustomization(customization, draft);
  }, [customization, draft]);

  // Compute CSS vars for preview
  const cssVars = useMemo(() => {
    const cs: any = currentCustomization?.colorScheme ?? defaults.colorScheme;
    const primary = cs.primary ?? cs.primaryBg;
    const primaryFg = cs.primaryForeground ?? cs.primaryText;
    const secondary = cs.secondary ?? cs.secondaryBg;
    const secondaryFg = cs.secondaryForeground ?? cs.secondaryText;
    const accent = cs.accent ?? cs.accentBg ?? cs.accent;
    const accentFg =
      cs.accentForeground ?? cs.accentText ?? cs.accentForeground;
    const highlight = cs.highlight ?? cs.highlightBg ?? cs.highlight;
    const highlightFg =
      cs.highlightForeground ?? cs.highlightText ?? cs.highlightForeground;
    const muted = cs.muted ?? cs.mutedBg ?? cs.muted;
    const mutedFg = cs.mutedForeground ?? cs.mutedText ?? cs.mutedForeground;
    const destructive = cs.destructive ?? cs.destructiveBg ?? cs.destructive;
    const destructiveFg =
      cs.destructiveForeground ??
      cs.destructiveText ??
      cs.destructiveForeground;

    return {
      ["--league-primary" as any]: primary,
      ["--league-primary-foreground" as any]: primaryFg,
      ["--league-secondary" as any]: secondary,
      ["--league-secondary-foreground" as any]: secondaryFg,
      ["--league-accent" as any]: accent,
      ["--league-accent-foreground" as any]: accentFg,
      ["--league-highlight" as any]: highlight,
      ["--league-highlight-foreground" as any]: highlightFg,
      ["--league-muted" as any]: muted,
      ["--league-muted-foreground" as any]: mutedFg,
      ["--league-destructive" as any]: destructive,
      ["--league-destructive-foreground" as any]: destructiveFg,
      // Legacy variables for backward compatibility with existing tests/styles
      ["--league-primary-bg" as any]: primary,
      ["--league-secondary-bg" as any]: secondary,
      ["--league-accent-bg" as any]: accent,
      ["--league-navbar-border-color" as any]: cs.navbarBorder || secondary,
    } as React.CSSProperties;
  }, [currentCustomization, defaults]);

  // Feature toggles for gating tabs/sections
  const { data: ftBranding } = useFeatureCheck(FEATURE_LEAGUE_CUSTOMIZATION);
  const { data: ftColors } = useFeatureCheck(
    FEATURE_LEAGUE_CUSTOMIZATION_COLORSCHEME
  );
  const showBranding = ftBranding?.enabled ?? false;
  const showColors = ftColors?.enabled ?? false;

  // Broadcast current customization to live layout for real-time preview
  useEffect(() => {
    if (currentCustomization) {
      setLiveDraft(currentCustomization);
    }
  }, [currentCustomization, setLiveDraft]);

  if (isLoading || !customization) {
    return <div className="min-h-[200px]" />;
  }

  if (!league?.can_customize_league) {
    // Gate access if navigated directly
    return (
      <Card className="bg-secondary text-secondary-foreground">
        <CardHeader>
          <CardTitle>{t("no_access_title", { default: "Settings" })}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm opacity-80">{t("no_access")}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-secondary text-secondary-foreground" style={cssVars}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{t("title")}</CardTitle>
        <div className="flex gap-2">
          <Button
            className="text-black"
            size="sm"
            onClick={() => clearDraft()}
            variant="outline"
            disabled={!isDirty}
          >
            {t("buttons.reset")}
          </Button>
          <Button
            size="sm"
            onClick={async () => {
              if (!currentCustomization) return;

              let payload: LeagueCustomization = currentCustomization;
              try {
                if (
                  logoFile &&
                  currentCustomization.logoUrl?.startsWith("blob:")
                ) {
                  const url = await uploadImageToCloudinary(logoFile);
                  payload = { ...currentCustomization, logoUrl: url };
                }
              } catch {
                toast.error(
                  t("branding.upload_failed", { default: "Logo upload failed" })
                );
                return;
              }

              updateMutation.mutate(payload, {
                onSuccess: () => {
                  clearDraft();
                  setLogoFile(null);
                  setLiveDraft(null);
                },
              });
            }}
            disabled={updateMutation.isPending || !isDirty}
          >
            {updateMutation.isPending ? t("buttons.saving") : t("buttons.save")}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue={"config"} className="space-y-4">
          <TabsList className="flex justify-start bg-muted rounded-lg p-1">
            <TabsTrigger value="config">{t("tabs.config")}</TabsTrigger>
            {showBranding && (
              <TabsTrigger value="branding">{t("tabs.branding")}</TabsTrigger>
            )}
            {showColors && (
              <TabsTrigger value="colors">{t("tabs.colors")}</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="config">
            <div className="space-y-6">
              <LeagueConfiguration
                league={league}
                showVisibility={showBranding}
                showStartingControls={true}
              />

              {/* League-wide showcased users are managed via the Manage Showcase drawer */}
            </div>
          </TabsContent>

          {showBranding && currentCustomization && (
            <TabsContent value="branding">
              <div className="space-y-6">
                <BrandingSection
                  value={{
                    logoUrl: currentCustomization.logoUrl,
                    logoPosition: currentCustomization.logoPosition,
                    logoSize: currentCustomization.logoSize,
                  }}
                  onFileSelected={(f) => setLogoFile(f)}
                  onChange={(patch) => updateDraft(patch)}
                />

                <HeaderCustomization
                  value={currentCustomization}
                  onChange={(patch) => updateDraft(patch)}
                />
              </div>
            </TabsContent>
          )}

          {showColors && currentCustomization && (
            <TabsContent value="colors">
              <ColorSchemeSection
                value={currentCustomization.colorScheme}
                onChange={(patch) =>
                  updateDraft({
                    colorScheme: {
                      ...currentCustomization.colorScheme,
                      ...patch,
                    },
                  })
                }
                onReset={() =>
                  updateDraft({ colorScheme: defaults.colorScheme })
                }
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
