"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect, useMemo, useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import FootballSpinner from "../../ui/football-spinner";
import { toast } from "sonner";
import { useLeagueCustomizationLive } from "@/src/components/league/layout/LeagueCustomizationProvider";
import { useLeagueData } from "@/src/hooks/use-league-data";
import { useLeagueRoundScores } from "@/src/hooks/use-league-round-scores";
import { useLeagueShowcasedUsers } from "@/src/hooks/use-league-showcased-users";
import { usePresenterWindow } from "@/src/hooks/use-presenter-window";
import { usePublishRound } from "@/src/hooks/use-round-visibility";
import { useRoundVisibilityInfo } from "@/src/hooks/use-round-visibility-info";
import { useTranslations } from "next-intl";

export function RoundShowcase({ id }: { id: string }) {
  const t = useTranslations("league_page.showcase");
  const { data: league, isLoading: leagueLoading } = useLeagueData(id);
  const { data: visInfo, isLoading: visLoading } = useRoundVisibilityInfo(id);
  const { data: leagueShowcased } = useLeagueShowcasedUsers(id);
  const { effective: customization } = useLeagueCustomizationLive();

  const matchdayOptions = useMemo(
    () =>
      visInfo?.per_round
        ?.filter((r) => !r.finished || !r.visible)
        .map((r) => r.round_number)
        .sort((a, b) => a - b) ?? [],
    [visInfo]
  );

  const [selectedRound, setSelectedRound] = useState<string>("");
  useEffect(() => {
    const selectedRoundInMatchdayOptions = matchdayOptions.includes(
      Number(selectedRound)
    );

    const shouldFallbackToDefaultRound =
      (!selectedRound || !selectedRoundInMatchdayOptions) &&
      matchdayOptions.length > 0;

    if (shouldFallbackToDefaultRound) {
      setSelectedRound(String(matchdayOptions[0]));
    }
  }, [matchdayOptions, selectedRound]);

  const roundNumber = useMemo(
    () => (selectedRound ? Number(selectedRound) : undefined),
    [selectedRound]
  );

  const currentRv = useMemo(
    () => visInfo?.per_round?.find((r) => r.round_number === roundNumber),
    [visInfo, roundNumber]
  );

  const publishMut = usePublishRound(id);

  const {
    data: scoresResp,
    isLoading,
    error,
  } = useLeagueRoundScores(id, roundNumber);

  const sortedScores = useMemo(() => {
    const items = scoresResp?.scores ?? [];
    return [...items].sort((a, b) => a.total_points - b.total_points);
  }, [scoresResp]);

  const showcasedScores = useMemo(() => {
    const ids = leagueShowcased?.showcased_user_ids ?? [];
    const scores = leagueShowcased?.scores ?? [];
    return scores
      .filter((s) => ids.includes(s.user_id))
      .sort((a, b) => a.total_points - b.total_points);
  }, [leagueShowcased]);

  // Separate state for main scores
  const [revealed, setRevealed] = useState<Record<number, boolean>>({});
  const [autoRevealActive, setAutoRevealActive] = useState(false);
  const [currentRevealIndex, setCurrentRevealIndex] = useState(0);

  // Separate state for showcased scores
  const [revealedShowcased, setRevealedShowcased] = useState<
    Record<number, boolean>
  >({});
  const [autoRevealActiveShowcased, setAutoRevealActiveShowcased] =
    useState(false);
  const [currentRevealIndexShowcased, setCurrentRevealIndexShowcased] =
    useState(0);

  const highlightCount = (league as any)?.showcase_user_limit as
    | number
    | undefined;

  useEffect(() => {
    setRevealed({});
    setAutoRevealActive(false);
    setCurrentRevealIndex(0);
    setRevealedShowcased({});
    setAutoRevealActiveShowcased(false);
    setCurrentRevealIndexShowcased(0);
  }, [roundNumber]);

  // Auto-reveal timer for main scores
  useEffect(() => {
    if (!autoRevealActive || currentRevealIndex >= sortedScores.length) {
      if (currentRevealIndex >= sortedScores.length && autoRevealActive) {
        setAutoRevealActive(false);
      }
      return;
    }

    const timer = setTimeout(() => {
      const user = sortedScores[currentRevealIndex];
      setRevealed((prev) => ({ ...prev, [user.user_id]: true }));
      setCurrentRevealIndex((prev) => prev + 1);
    }, 2000);

    return () => clearTimeout(timer);
  }, [autoRevealActive, currentRevealIndex, sortedScores]);

  // Auto-reveal timer for showcased scores
  useEffect(() => {
    if (
      !autoRevealActiveShowcased ||
      currentRevealIndexShowcased >= showcasedScores.length
    ) {
      if (
        currentRevealIndexShowcased >= showcasedScores.length &&
        autoRevealActiveShowcased
      ) {
        setAutoRevealActiveShowcased(false);
      }
      return;
    }

    const timer = setTimeout(() => {
      const user = showcasedScores[currentRevealIndexShowcased];
      setRevealedShowcased((prev) => ({ ...prev, [user.user_id]: true }));
      setCurrentRevealIndexShowcased((prev) => prev + 1);
    }, 2000);

    return () => clearTimeout(timer);
  }, [autoRevealActiveShowcased, currentRevealIndexShowcased, showcasedScores]);

  // Presenter windows
  const scoresPresenter = usePresenterWindow({
    title: `Score Presenter - Matchday ${roundNumber}`,
    brandingLogoUrl: customization?.logoUrl || null,
    brandingText: customization?.customHeader || undefined,
    primaryColor: customization?.colorScheme.primary,
    secondaryColor: customization?.colorScheme.secondary,
    tertiaryColor: customization?.colorScheme.accent,
    content: (_data: any) => `
      content.innerHTML = data.sortedScores.map((s, idx) => {
        const isRevealed = data.revealed[s.user_id];
        const isHighlighted = data.highlightCount > 0 && idx >= data.sortedScores.length - data.highlightCount;
        const rank = data.sortedScores.length - idx;
        const rankClass = rank === 1 ? 'rank-1' : rank === 2 ? 'rank-2' : rank === 3 ? 'rank-3' : 'rank-other';
        
        return \`
          <div class="item \${isRevealed ? 'revealed' : ''} \${isHighlighted ? 'highlighted' : ''}" style="position: relative;">
            <div class="rank \${rankClass}">\${isRevealed ? rank : '?'}</div>
            <div class="username">\${isRevealed ? s.username : '???'}</div>
            \${isRevealed ? \`
              <div class="points">
                <div class="points-value">\${s.total_points}</div>
                <div class="points-label">points</div>
              </div>
              <div class="stats">
                <div class="stat-item">
                  <div class="stat-icon perfect">✓</div>
                  <span>\${s.perfect || 0}</span>
                </div>
                <div class="stat-item">
                  <div class="stat-icon correct">~</div>
                  <span>\${s.correct || 0}</span>
                </div>
              </div>
              \${isHighlighted ? \`
                <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              \` : ''}
              \${isHighlighted ? \`<div class="top-badge">TOP \${data.highlightCount}</div>\` : ''}
            \` : ''}
          </div>
        \`;
      }).join('');
    `,
    additionalStyles: `
      .stats {
        display: flex;
        gap: 0.75rem;
        align-items: center;
      }
      .stat-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 1rem;
        font-weight: 600;
      }
      .stat-icon {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 700;
      }
      .stat-icon.perfect {
        background: #10b981;
        color: white;
      }
      .stat-icon.correct {
        background: #f59e0b;
        color: white;
      }
    `,
  });

  const showcasedPresenter = usePresenterWindow({
    title: `League Showcased - Matchday ${roundNumber}`,
    brandingLogoUrl: customization?.logoUrl || null,
    brandingText: customization?.customHeader || undefined,
    primaryColor: customization?.colorScheme.primary,
    secondaryColor: customization?.colorScheme.secondary,
    tertiaryColor: customization?.colorScheme.accent,
    content: (_data: any) => `
      const rows = data.showcasedScores || [];
      
      if (rows.length === 0) {
        content.innerHTML = '<div class="item revealed"><div class="username">No league-level showcased users configured.</div></div>';
      } else {
        content.innerHTML = rows.map((s, idx) => {
          const isRevealed = data.revealedShowcased[s.user_id];
          const rank = rows.length - idx;
          const rankClass = rank === 1 ? 'rank-1' : rank === 2 ? 'rank-2' : rank === 3 ? 'rank-3' : 'rank-other';
          
          return \`
            <div class="item \${isRevealed ? 'revealed' : ''}">
              <div class="rank \${rankClass}">\${isRevealed ? rank : '?'}</div>
              <div class="showcased-icon">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
              <div class="username">\${isRevealed ? s.username : '???'}</div>
              \${isRevealed ? \`
                <div class="points">
                  <div class="points-value">\${s.total_points}</div>
                  <div class="points-label">points</div>
                </div>
                <div class="stats">
                  <div class="stat-item">
                    <div class="stat-icon perfect">✓</div>
                    <span>\${s.perfect || 0}</span>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon correct">~</div>
                    <span>\${s.correct || 0}</span>
                  </div>
                </div>
              \` : ''}
            </div>
          \`;
        }).join('');
      }
    `,
    additionalStyles: `
      .stats {
        display: flex;
        gap: 0.75rem;
        align-items: center;
      }
      .stat-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 1rem;
        font-weight: 600;
      }
      .stat-icon {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 700;
      }
      .stat-icon.perfect {
        background: #10b981;
        color: white;
      }
      .stat-icon.correct {
        background: #f59e0b;
        color: white;
      }
    `,
  });

  // Sync state to presenter windows
  useEffect(() => {
    scoresPresenter.updateState({
      revealed,
      autoRevealActive,
      currentRevealIndex,
      sortedScores,
      highlightCount,
      roundNumber,
    });
  }, [
    revealed,
    autoRevealActive,
    currentRevealIndex,
    sortedScores,
    highlightCount,
    roundNumber,
    scoresPresenter,
  ]);

  useEffect(() => {
    showcasedPresenter.updateState({
      revealedShowcased,
      autoRevealActive: autoRevealActiveShowcased,
      currentRevealIndex: currentRevealIndexShowcased,
      showcasedScores,
      roundNumber,
    });
  }, [
    revealedShowcased,
    autoRevealActiveShowcased,
    currentRevealIndexShowcased,
    showcasedScores,
    roundNumber,
    showcasedPresenter,
  ]);

  // Main scores controls
  const revealAll = () => {
    setAutoRevealActive(false);
    sortedScores.forEach((s, idx) => {
      setTimeout(() => {
        setRevealed((prev) => ({ ...prev, [s.user_id]: true }));
      }, idx * 150);
    });
  };

  const startOneByOne = () => {
    setRevealed({});
    setCurrentRevealIndex(0);
    setAutoRevealActive(true);
  };

  const revealNext = () => {
    if (currentRevealIndex < sortedScores.length) {
      const user = sortedScores[currentRevealIndex];
      setRevealed((prev) => ({ ...prev, [user.user_id]: true }));
      setCurrentRevealIndex((prev) => prev + 1);
    }
  };

  const stopReveal = () => {
    setAutoRevealActive(false);
  };

  const resetReveal = () => {
    setRevealed({});
    setCurrentRevealIndex(0);
    setAutoRevealActive(false);
  };

  // Showcased scores controls
  const revealAllShowcased = () => {
    setAutoRevealActiveShowcased(false);
    showcasedScores.forEach((s, idx) => {
      setTimeout(() => {
        setRevealedShowcased((prev) => ({ ...prev, [s.user_id]: true }));
      }, idx * 150);
    });
  };

  const startOneByOneShowcased = () => {
    setRevealedShowcased({});
    setCurrentRevealIndexShowcased(0);
    setAutoRevealActiveShowcased(true);
  };

  const revealNextShowcased = () => {
    if (currentRevealIndexShowcased < showcasedScores.length) {
      const user = showcasedScores[currentRevealIndexShowcased];
      setRevealedShowcased((prev) => ({ ...prev, [user.user_id]: true }));
      setCurrentRevealIndexShowcased((prev) => prev + 1);
    }
  };

  const stopRevealShowcased = () => {
    setAutoRevealActiveShowcased(false);
  };

  const resetRevealShowcased = () => {
    setRevealedShowcased({});
    setCurrentRevealIndexShowcased(0);
    setAutoRevealActiveShowcased(false);
  };

  const visibilityErrorKey =
    (error as any)?.error_key || (error as any)?.body?.error_key;

  if (leagueLoading || visLoading || isLoading) {
    return (
      <div>
        <div className="text-center text-lg"></div>
        <div className="flex justify-center space-x-4">
          <div className="flex flex-col items-center gap-6">
            <FootballSpinner variant="lg" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="text-muted-foreground text-sm">
          {t("description", {
            default: "Showcase top performers for a round before publishing.",
          })}
        </div>
        <Select value={selectedRound} onValueChange={setSelectedRound}>
          <SelectTrigger className="w-[200px]">
            <SelectValue
              placeholder={t("round_selector", { default: "Select round" })}
            />
          </SelectTrigger>
          <SelectContent>
            {matchdayOptions.map((md) => (
              <SelectItem key={md} value={String(md)}>
                {t("matchday", { default: `Matchday ${md}`, matchday: md })}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {currentRv && !currentRv.visible && (
        <div className="flex items-center justify-end">
          <Button
            onClick={async () => {
              if (!roundNumber) return;
              try {
                await publishMut.mutateAsync({ roundNumber });
                toast.success(
                  t("published", { default: "Published", roundNumber })
                );
              } catch (e: any) {
                toast.error(
                  e?.message ||
                    t("error", { default: "Failed to load round scores." })
                );
              }
            }}
            disabled={publishMut.isPending}
          >
            {publishMut.isPending
              ? t("publishing", { default: "Publishing..." })
              : t("publish_round", { default: "Publish round" })}
          </Button>
        </div>
      )}

      {!isLoading &&
        error &&
        visibilityErrorKey === "LEAGUE_ROUND_SCORES_HIDDEN" && (
          <Alert>
            <AlertDescription>
              {t("hidden", {
                default: "Round scores are hidden by the league owner.",
              })}
            </AlertDescription>
          </Alert>
        )}

      {!isLoading &&
        error &&
        visibilityErrorKey !== "LEAGUE_ROUND_SCORES_HIDDEN" && (
          <Alert>
            <AlertDescription>
              {t("error", { default: "Failed to load round scores." })}
            </AlertDescription>
          </Alert>
        )}

      {!isLoading && !error && (
        <div className="space-y-6">
          {/* Scores Presenter */}
          <div className="flex flex-col gap-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">
                {t("scores_presenter", { default: "Scores Presenter" })}
              </h3>
              <div className="text-sm text-muted-foreground">
                {t("revealed_count", {
                  default: "Revealed {count}/{total}",
                  count: Object.values(revealed).filter(Boolean).length,
                  total: sortedScores.length,
                })}
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button onClick={() => scoresPresenter.open()} variant="outline">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
                {t("open_window", { default: "Open window" })}
              </Button>

              {!autoRevealActive ? (
                <>
                  <Button
                    onClick={revealNext}
                    disabled={sortedScores.length === 0}
                  >
                    {t("next", { default: "Next" })}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={startOneByOne}
                    disabled={sortedScores.length === 0}
                  >
                    {t("one_by_one", { default: "One by one" })}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={revealAll}
                    disabled={sortedScores.length === 0}
                  >
                    {t("show_all", { default: "Show all" })}
                  </Button>
                  {Object.values(revealed).filter(Boolean).length > 0 && (
                    <Button variant="outline" onClick={resetReveal}>
                      {t("reset", { default: "Reset" })}
                    </Button>
                  )}
                </>
              ) : (
                <>
                  <Button onClick={revealNext}>
                    {t("next", { default: "Next" })}
                  </Button>
                  <Button variant="outline" onClick={stopReveal}>
                    {t("stop", { default: "Stop" })}
                  </Button>
                  <Button variant="outline" onClick={resetReveal}>
                    {t("reset", { default: "Reset" })}
                  </Button>
                </>
              )}
            </div>

            {autoRevealActive && (
              <div className="bg-primary/10 border border-primary/20 rounded-lg px-4 py-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  <span className="text-sm font-medium">
                    {t("auto_revealing", { default: "Auto-revealing..." })}
                  </span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {t("next_in", { default: "Next in 2s" })}
                </span>
              </div>
            )}
          </div>

          {/* Showcased Presenter */}
          <div className="flex flex-col gap-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">
                {t("showcased_presenter", { default: "Showcased Presenter" })}
              </h3>
              <div className="text-sm text-muted-foreground">
                {t("revealed_count", {
                  default: "Revealed {count}/{total}",
                  count:
                    Object.values(revealedShowcased).filter(Boolean).length,
                  total: showcasedScores.length,
                })}
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => showcasedPresenter.open()}
                variant="outline"
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
                {t("open_window", { default: "Open window" })}
              </Button>

              {!autoRevealActiveShowcased ? (
                <>
                  <Button
                    onClick={revealNextShowcased}
                    disabled={showcasedScores.length === 0}
                  >
                    {t("next", { default: "Next" })}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={startOneByOneShowcased}
                    disabled={showcasedScores.length === 0}
                  >
                    {t("one_by_one", { default: "One by one" })}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={revealAllShowcased}
                    disabled={showcasedScores.length === 0}
                  >
                    {t("show_all", { default: "Show all" })}
                  </Button>
                  {Object.values(revealedShowcased).filter(Boolean).length >
                    0 && (
                    <Button variant="outline" onClick={resetRevealShowcased}>
                      {t("reset", { default: "Reset" })}
                    </Button>
                  )}
                </>
              ) : (
                <>
                  <Button onClick={revealNextShowcased}>
                    {t("next", { default: "Next" })}
                  </Button>
                  <Button variant="outline" onClick={stopRevealShowcased}>
                    {t("stop", { default: "Stop" })}
                  </Button>
                  <Button variant="outline" onClick={resetRevealShowcased}>
                    {t("reset", { default: "Reset" })}
                  </Button>
                </>
              )}
            </div>

            {autoRevealActiveShowcased && (
              <div className="bg-primary/10 border border-primary/20 rounded-lg px-4 py-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  <span className="text-sm font-medium">
                    {t("auto_revealing", { default: "Auto-revealing..." })}
                  </span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {t("next_in", { default: "Next in 2s" })}
                </span>
              </div>
            )}
          </div>

          {highlightCount ? (
            <div className="text-xs text-muted-foreground">
              {t("highlight_note", {
                default: "Top {count} highlighted",
                count: highlightCount,
              })}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
