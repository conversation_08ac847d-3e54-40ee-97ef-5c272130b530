"use client";

import {
  AlertCircle,
  Calendar,
  CheckCircle,
  MapPin,
  Save,
  Trophy,
  XCircle,
} from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { TeamColors, getTeamColors } from "./utils";
import { memo, useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { CldImage } from "next-cloudinary";
import FormIndicator from "./form-indicator";
import { GradientBadge } from "../ui/gradient-badge";
import type React from "react";
import { cn } from "@/src/libs/utils";
import { getUrl } from "@/src/libs/helpers";
import styles from "./match-card.module.scss";
import useScreenSize from "@/src/hooks/use-screen-size";
import { useTranslations } from "next-intl";

type MatchPrediction = {
  match_id: number;
  home_score: number;
  away_score: number;
  points?: 0 | 1 | 3;
};

interface FullColorMatchCardProps {
  id: number;
  home_team: Team;
  away_team: Team;
  venue: string;
  utc_date: string;
  status: MatchStatus;
  matchday: number;
  score?: Score;
  prediction?: MatchPrediction;
  isPredicting?: boolean;
  setMatchScores?: (callback: (prevScores: any) => any) => void;
  hasSavedPrediction?: boolean;
  hasUnsavedChanges?: boolean;
}

function FullColorMatchCard({
  id,
  home_team,
  away_team,
  venue,
  utc_date,
  status,
  matchday,
  score,
  prediction,
  isPredicting = true,
  setMatchScores,
  hasSavedPrediction = false,
  hasUnsavedChanges = false,
}: FullColorMatchCardProps) {
  const t = useTranslations("league_page.match_card");
  const [homeScore, setHomeScore] = useState<number | undefined>(
    prediction?.home_score
  );
  const [awayScore, setAwayScore] = useState<number | undefined>(
    prediction?.away_score
  );

  // Track if the current form values are different from the original prediction
  const [localHasUnsavedChanges, setLocalHasUnsavedChanges] =
    useState(hasUnsavedChanges);

  // Update local unsaved changes state when form values change
  useEffect(() => {
    if (prediction) {
      const scoreChanged =
        homeScore !== prediction.home_score ||
        awayScore !== prediction.away_score;

      setLocalHasUnsavedChanges(scoreChanged || hasUnsavedChanges);
    }
  }, [homeScore, awayScore, prediction, hasUnsavedChanges]);
  const isFinished = status === "FINISHED";
  const matchDate = new Date(utc_date);

  // Get team colors from club_colors string
  const homeTeamColors = getTeamColors(home_team.club_colors);
  const awayTeamColors = getTeamColors(away_team.club_colors);

  // Update match scores when user inputs change
  useEffect(() => {
    // Only update parent state if both scores are defined and we're in prediction mode
    if (isPredicting && homeScore !== undefined && awayScore !== undefined) {
      // Create a stable reference to the current scores
      const currentHomeScore = homeScore;
      const currentAwayScore = awayScore;

      setMatchScores?.((prevMatchScores: any) => {
        // Check if we already have this match with the same scores
        const existingMatch = prevMatchScores.find(
          (match: any) => match.match_id === id
        );

        // If scores haven't changed, don't trigger an update
        if (
          existingMatch &&
          existingMatch.home_score === currentHomeScore &&
          existingMatch.away_score === currentAwayScore
        ) {
          return prevMatchScores; // Return the same array reference to prevent re-render
        }

        const existingMatchIndex = prevMatchScores.findIndex(
          (match: any) => match.match_id === id
        );

        if (existingMatchIndex !== -1) {
          return prevMatchScores.map((match: any, index: number) => {
            if (index === existingMatchIndex) {
              return {
                ...match,
                home_score: currentHomeScore,
                away_score: currentAwayScore,
              };
            }
            return match;
          });
        }

        return [
          ...prevMatchScores,
          {
            match_id: id,
            home_score: currentHomeScore,
            away_score: currentAwayScore,
          },
        ];
      });
    }
  }, [homeScore, awayScore, id, isPredicting, setMatchScores]);

  const matchIsLive = (status: string) => {
    return status === "LIVE" || status === "IN_PLAY" || status === "PAUSED";
  };

  const matchAboutToStart = () => {
    const matchTime = matchDate.getTime();
    const currentTime = new Date().getTime();
    const timeDiff = matchTime - currentTime;
    return timeDiff <= 300000 && (status === "SCHEDULED" || status === "TIMED");
  };

  const getPredictionResult = () => {
    if (!isFinished || !prediction || !score) return null;
    if (prediction.points === 3) {
      return {
        points: 3,
        label: t("scoring.perfect"),
        icon: <Trophy className="h-4 w-4" />,
      };
    }
    if (prediction.points === 1) {
      return {
        points: 1,
        label: t("scoring.correct"),
        icon: <CheckCircle className="h-4 w-4" />,
      };
    }
    if (prediction.points === 0) {
      return {
        points: 0,
        label: t("scoring.wrong"),
        icon: <XCircle className="h-4 w-4" />,
      };
    }
    return null;
  };

  const predictionResult = getPredictionResult();

  const setTeamColorCssVariables = () => {
    const homeColor = homeTeamColors.primary;
    const homeColorSecondary = homeTeamColors.secondary || homeColor;
    const awayColor = awayTeamColors.primary;
    const awayColorSecondary = awayTeamColors.secondary || awayColor;

    return {
      "--home-color": homeColor,
      "--home-color-secondary": homeColorSecondary,
      "--away-color": awayColor,
      "--away-color-secondary": awayColorSecondary,
    } as React.CSSProperties;
  };

  // Function to generate a contrasting background for the score section
  const getScoreSectionStyle = () => {
    return {
      background: "rgba(255, 255, 255, 0.15)",
      backdropFilter: "blur(4px)",
      boxShadow: "0 0 8px 0 rgba(0, 0, 0, 0.6)",
      border: "1px solid rgba(255, 255, 255, 0.15)",
    };
  };

  const getStatusBadge = () => {
    switch (status) {
      case "IN_PLAY":
      case "PAUSED":
        return (
          <Badge variant="destructive" className="animate-pulse">
            {t("ongoing")}
          </Badge>
        );
      case "POSTPONED":
        return (
          <Badge
            variant="secondary"
            className="bg-black/20 text-white border-white/20"
          >
            {t("prediction_closed")}
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Card
      className={cn(
        "overflow-hidden relative flex flex-col border-none bg-muted",
        styles["match-card"]
      )}
      data-testid="match-card"
      style={setTeamColorCssVariables()}
    >
      <CardContent
        className="p-1 xs:p-6 xs:py-1 flex flex-grow flex-col"
        removePadding={true}
      >
        <div
          data-testid="match-card-content"
          className="grid grid-cols-1 gap-2"
        >
          {/* Match Info Row */}
          <div className="flex justify-between items-center">
            <div className="flex items-center bg-black/60 rounded-md px-1.5 py-0.5">
              <MapPin className="h-3 w-3 mr-1 text-white" />
              <span className="text-xs text-white font-medium truncate max-w-[150px]">
                {venue}
              </span>
            </div>
            <div className="flex flex-wrap items-center gap-2">
              {isPredicting && (
                <PredictionStatusIndicator
                  hasSavedPrediction={hasSavedPrediction}
                  hasUnsavedChanges={localHasUnsavedChanges}
                />
              )}
              <div className="flex items-center bg-black/60 rounded-md px-1.5 py-0.5">
                <Calendar className="h-3 w-3 mr-1 text-white" />
                <span className="text-xs text-white font-medium">
                  {t("matchday", { matchday })}
                </span>
              </div>
              {getStatusBadge() && (
                <div className="ml-2">{getStatusBadge()}</div>
              )}
            </div>
          </div>
          {/* Team Badges Row */}
          <div className="flex justify-between items-center">
            <GradientBadge
              main={homeTeamColors.primary}
              secondary={homeTeamColors.secondary || homeTeamColors.primary}
              tertiary={homeTeamColors.tertiary}
              className="h-6 w-16"
            />
            <GradientBadge
              main={awayTeamColors.primary}
              secondary={awayTeamColors.secondary || awayTeamColors.primary}
              tertiary={awayTeamColors.tertiary}
              className="h-6 w-16"
            />
          </div>
          {/* Teams and Score row */}
          <div className={cn("", styles["team-score-row"])}>
            {/* Home Team Section */}
            <TeamSection team={home_team} />
            {/* Score Section */}
            <div className="flex flex-col items-center justify-center px-2 min-w-[80px]">
              <div
                className="rounded-lg p-2 w-full"
                style={getScoreSectionStyle()}
              >
                {renderScoreSection()}
              </div>
            </div>
            {/* Away Team Section */}
            <TeamSection team={away_team} />
          </div>
        </div>
      </CardContent>

      <CardFooter className="w-full mt-0 xs:pt-0">
        {/* Form Indicator or Prediction Result */}
        {isPredicting && !isFinished ? (
          /* Form Indicators for Predict page */
          <InfoRow
            homeTeamColors={homeTeamColors}
            awayTeamColors={awayTeamColors}
          >
            <FormIndicatorsDisplay homeTeam={home_team} awayTeam={away_team} />
          </InfoRow>
        ) : (
          /* Prediction Result for Results page */
          (isFinished || matchIsLive(status)) &&
          prediction && (
            <InfoRow
              homeTeamColors={homeTeamColors}
              awayTeamColors={awayTeamColors}
            >
              <PredictionResultDisplay
                prediction={prediction}
                predictionResult={predictionResult}
              />
            </InfoRow>
          )
        )}
      </CardFooter>
    </Card>
  );

  function renderScoreSection() {
    if (isPredicting) {
      // Check if match is about to start - prevent predictions if it is
      if (matchAboutToStart()) {
        return <PredictionClosed prediction={prediction} />;
      }

      if (
        status === "POSTPONED" ||
        status === "SCHEDULED" ||
        status === "TIMED"
      ) {
        return (
          <ScoreInputs
            homeTeam={home_team}
            awayTeam={away_team}
            homeScore={homeScore}
            awayScore={awayScore}
            setHomeScore={setHomeScore}
            setAwayScore={setAwayScore}
          />
        );
      }
      if (matchIsLive(status)) {
        return <LiveMatchIndicator />;
      }
    }

    if (status === "POSTPONED") {
      return (
        <span className="text-amber-400 text-xs font-medium">
          {t("prediction_closed")}
        </span>
      );
    }

    if (matchIsLive(status)) {
      return <LiveMatchIndicator />;
    }

    return <GameScore home={score?.fullTimeHome} away={score?.fullTimeAway} />;
  }
}

export default memo(FullColorMatchCard);

// Helper Components

interface InfoRowProps {
  children: React.ReactNode;
  homeTeamColors: TeamColors;
  awayTeamColors: TeamColors;
}

const InfoRow = ({ children }: InfoRowProps) => (
  <div
    className="mt-2 flex justify-between items-center text-xs backdrop-blur-sm rounded-md p-1.5 w-full h-8"
    style={{
      background:
        "linear-gradient(110deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.4) 45%, rgba(0, 0, 0, 0.4) 55%, rgba(0, 0, 0, 0.5) 100%)",
      color: "rgb(255, 255, 255)",
    }}
  >
    {children}
  </div>
);

interface FormIndicatorsDisplayProps {
  homeTeam: Team;
  awayTeam: Team;
}

const FormIndicatorsDisplay = ({
  homeTeam,
  awayTeam,
}: FormIndicatorsDisplayProps) => {
  const t = useTranslations("league_page.match_card");
  return (
    <>
      <div className="flex items-center">
        {homeTeam.form ? (
          homeTeam.form.matches && homeTeam.form.matches.length > 0 ? (
            <FormIndicator
              form={homeTeam.form.string}
              matches={homeTeam.form.matches}
              position="left"
            />
          ) : (
            <FormIndicator form={homeTeam.form.string} />
          )
        ) : (
          <span className="opacity-70">
            {t("no_data", { defaultValue: "No data" })}
          </span>
        )}
      </div>
      <div className="flex items-center">
        {awayTeam.form ? (
          awayTeam.form.matches && awayTeam.form.matches.length > 0 ? (
            <FormIndicator
              form={awayTeam.form.string}
              matches={awayTeam.form.matches}
              position="right"
            />
          ) : (
            <FormIndicator form={awayTeam.form.string} />
          )
        ) : (
          <span className="opacity-70">
            {t("no_data", { defaultValue: "No data" })}
          </span>
        )}
      </div>
    </>
  );
};

interface PredictionResultDisplayProps {
  prediction: MatchPrediction;
  predictionResult: {
    points: number;
    label: string;
    icon: React.ReactNode;
  } | null;
}

const PredictionResultDisplay = ({
  prediction,
  predictionResult,
}: PredictionResultDisplayProps) => {
  const t = useTranslations("league_page.match_card");
  return (
    <>
      <p className="font-medium">
        {t("your_prediction", {
          homeScore: prediction.home_score,
          awayScore: prediction.away_score,
        })}
      </p>
      {predictionResult && (
        <div className="flex items-center">
          <p className="mr-2 font-medium">
            {predictionResult.points} {t("pts")}
          </p>
          {predictionResult.icon}
        </div>
      )}
    </>
  );
};

const TeamSection = ({ team }: { team: Team }) => {
  // TODO: Create this as a container query for better management of size
  const { isLargeScreen, isXLargeScreen } = useScreenSize();

  const name = isXLargeScreen
    ? team.name
    : isLargeScreen
    ? team.shortName
    : team.tla;

  return (
    <div className="flex justify-center items-center text-balance text-center">
      <div className="flex flex-col items-start">
        <div className="flex flex-col gap-1 items-center">
          <TeamLogo team={team} />
          <p className="ml-2 font-bold text-sm text-blue-950">{name}</p>
        </div>
      </div>
    </div>
  );
};

const TeamLogo = ({ team }: { team: Team }) => {
  const { isLargeScreen } = useScreenSize();

  return (
    <div
      className="w-8 h-8 xs:w-12 xs:h-12 flex items-center justify-center rounded-full overflow-hidden flex-shrink-0 border-2"
      style={{
        borderColor: "rgba(255, 255, 255, 0.3)",
      }}
    >
      <CldImage
        src={getUrl(team.crest_public_id)}
        alt={team.name}
        width={isLargeScreen ? 48 : 36}
        height={isLargeScreen ? 48 : 36}
        className="object-contain"
      />
    </div>
  );
};

interface ScoreInputsProps {
  homeTeam: Team;
  awayTeam: Team;
  homeScore: number | undefined;
  awayScore: number | undefined;
  setHomeScore: (score: number | undefined) => void;
  setAwayScore: (score: number | undefined) => void;
}

const ScoreInputs = ({
  homeTeam,
  awayTeam,
  homeScore,
  awayScore,
  setHomeScore,
  setAwayScore,
}: ScoreInputsProps) => {
  const t = useTranslations("league_page.match_card");
  const hasError =
    (homeScore !== undefined && awayScore === undefined) ||
    (homeScore === undefined && awayScore !== undefined);

  const homeTeamColors = getTeamColors(homeTeam.club_colors);
  const awayTeamColors = getTeamColors(awayTeam.club_colors);

  return (
    <div className="flex items-center justify-center">
      <ScoreInput
        setScore={setHomeScore}
        score={homeScore}
        hasError={hasError}
        aria-label={t("ally.score_input.home", { teamName: homeTeam.name })}
        teamColor={homeTeamColors.primary}
      />
      <span className="mx-1 text-xs font-medium text-white">-</span>
      <ScoreInput
        setScore={setAwayScore}
        score={awayScore}
        hasError={hasError}
        aria-label={t("ally.score_input.away", { teamName: awayTeam.name })}
        teamColor={awayTeamColors.primary}
      />
    </div>
  );
};

interface ScoreInputProps {
  setScore: (score: number | undefined) => void;
  score: number | undefined;
  hasError?: boolean;
  teamColor: string; // We keep this for type safety but don't use it directly
  "aria-label"?: string;
}

const ScoreInput = ({
  setScore,
  score,
  hasError,
  "aria-label": ariaLabel,
}: ScoreInputProps) => (
  <input
    type="number"
    min="0"
    max="99"
    value={score === undefined ? "" : score}
    onChange={(e) => {
      const value = e.target.value;
      if (value === "") {
        setScore(undefined);
      } else {
        const numValue = Number.parseInt(value, 10);
        if (!isNaN(numValue) && numValue >= 0) {
          setScore(numValue);
        }
      }
    }}
    onKeyDown={(e) => {
      // Handle arrow up when input is empty
      if (e.key === "ArrowUp" && score === undefined) {
        e.preventDefault(); // Prevent default browser behavior
        setScore(0);
      }
    }}
    className={cn(
      "w-9 h-9 text-center text-sm font-bold border rounded-md text-black",
      hasError ? "border-red-500" : "border-white/30",
      "focus:outline-none focus:ring-2",
      // Hide spinner arrows
      "[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
    )}
    style={
      {
        "--tw-ring-color": "rgba(255, 255, 255, 0.5)",
        background: "rgba(0, 0, 0, 0.25)",
        textShadow: "0 1px 1px rgba(0, 0, 0, 0.5)",
        backdropFilter: "blur(2px)",
      } as React.CSSProperties
    }
    aria-label={ariaLabel}
  />
);

function GameScore({
  home,
  away,
}: {
  home?: number;
  away?: number;
  homeTeam?: Team; // Made optional and unused
  awayTeam?: Team; // Made optional and unused
}) {
  return (
    <div className="flex items-center justify-center py-1">
      <div className="text-lg font-bold w-8 text-center text-black drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
        {home ?? "-"}
      </div>
      <span className="mx-1 text-xs text-black drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
        -
      </span>
      <div className="text-lg font-bold w-8 text-center text-black drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
        {away ?? "-"}
      </div>
    </div>
  );
}

function PredictionClosed({ prediction }: { prediction?: MatchPrediction }) {
  const t = useTranslations("league_page.match_card");
  if (prediction) {
    return (
      <div className="text-xs text-center py-1">
        <p className="text-black font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
          {t("prediction_locked")}
        </p>
        <p className="text-black drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
          {prediction.home_score}-{prediction.away_score}
        </p>
      </div>
    );
  }
  return (
    <span className="text-xs text-black font-medium py-2 block drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
      {t("prediction_locked")}
    </span>
  );
}

function LiveMatchIndicator() {
  const t = useTranslations("league_page.match_card");
  return (
    <div className="text-center py-1">
      <span className="text-black text-xs font-medium animate-pulse flex items-center justify-center drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
        <span className="w-2 h-2 bg-red-500 rounded-full mr-1.5 animate-pulse"></span>
        {t("ongoing")}
      </span>
    </div>
  );
}

interface PredictionStatusIndicatorProps {
  hasSavedPrediction: boolean;
  hasUnsavedChanges: boolean;
}

function PredictionStatusIndicator({
  hasSavedPrediction,
  hasUnsavedChanges,
}: PredictionStatusIndicatorProps) {
  const t = useTranslations("league_page.match_card");
  if (!hasSavedPrediction && !hasUnsavedChanges) {
    return null;
  }

  if (hasUnsavedChanges) {
    return (
      <div className="flex items-center bg-amber-500/80 rounded-md px-1.5 py-0.5">
        <AlertCircle className="h-3 w-3 mr-1 text-white" />
        <span className="text-xs text-white font-medium">
          {t("unsaved", { defaultValue: "Unsaved" })}
        </span>
      </div>
    );
  }

  if (hasSavedPrediction) {
    return (
      <div className="flex items-center bg-green-500/80 rounded-md px-1.5 py-0.5">
        <Save className="h-3 w-3 mr-1 text-white" />
        <span className="text-xs text-white font-medium">
          {t("saved", { defaultValue: "Saved" })}
        </span>
      </div>
    );
  }

  return null;
}
